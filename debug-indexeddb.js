/**
 * IndexedDB Debug and Test Data Script
 * 
 * This script will:
 * 1. Check the current state of IndexedDB
 * 2. Add test trade data if database is empty
 * 3. Verify data insertion
 * 4. Trigger data refresh in the application
 */

console.log('🔍 Starting IndexedDB Debug and Data Population...');

// Database configuration
const DB_NAME = 'adhd-trading-dashboard';
const DB_VERSION = 2;
const STORES = ['trades', 'trade_fvg_details', 'trade_setups', 'trade_analysis'];

class IndexedDBDebugger {
  constructor() {
    this.db = null;
  }

  async init() {
    return new Promise((resolve, reject) => {
      console.log(`📊 Opening database: ${DB_NAME} v${DB_VERSION}`);
      
      const request = indexedDB.open(DB_NAME, DB_VERSION);
      
      request.onerror = () => {
        console.error('❌ Failed to open database:', request.error);
        reject(request.error);
      };
      
      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ Database opened successfully');
        console.log('📋 Available stores:', Array.from(this.db.objectStoreNames));
        resolve(this.db);
      };
      
      request.onupgradeneeded = (event) => {
        console.log('🔄 Database upgrade needed');
        const db = event.target.result;
        
        // Create stores if they don't exist
        STORES.forEach(storeName => {
          if (!db.objectStoreNames.contains(storeName)) {
            console.log(`➕ Creating store: ${storeName}`);
            const store = db.createObjectStore(storeName, { keyPath: 'id', autoIncrement: true });
            
            // Add indexes for trades store
            if (storeName === 'trades') {
              store.createIndex('date', 'date', { unique: false });
              store.createIndex('symbol', 'symbol', { unique: false });
              store.createIndex('result', 'result', { unique: false });
            }
          }
        });
      };
    });
  }

  async checkDataState() {
    console.log('\n🔍 Checking current data state...');
    
    const results = {};
    
    for (const storeName of STORES) {
      if (this.db.objectStoreNames.contains(storeName)) {
        const count = await this.getStoreCount(storeName);
        const sample = await this.getSampleData(storeName, 3);
        
        results[storeName] = { count, sample };
        console.log(`📊 ${storeName}: ${count} records`);
        
        if (sample.length > 0) {
          console.log(`   Sample data:`, sample[0]);
        }
      } else {
        console.log(`❌ Store ${storeName} does not exist`);
        results[storeName] = { count: 0, sample: [], error: 'Store not found' };
      }
    }
    
    return results;
  }

  async getStoreCount(storeName) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.count();
      
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  async getSampleData(storeName, limit = 5) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([storeName], 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();
      
      request.onsuccess = () => {
        const results = request.result.slice(0, limit);
        resolve(results);
      };
      request.onerror = () => reject(request.error);
    });
  }

  async addTestTrades() {
    console.log('\n➕ Adding test trade data...');
    
    const testTrades = [
      {
        id: 1,
        symbol: 'EURUSD',
        date: '2024-01-15',
        direction: 'long',
        quantity: 1,
        entry_price: 1.0850,
        exit_price: 1.0900,
        profit: 50,
        result: 'win',
        setup: 'Morning Breakout',
        session: 'NY AM',
        market: 'Forex',
        entry: 1.0850,
        exit: 1.0900,
        pnl: 50,
        rMultiple: 1.5,
        win: true,
        timestamp: new Date('2024-01-15T09:30:00Z').toISOString()
      },
      {
        id: 2,
        symbol: 'GBPUSD',
        date: '2024-01-14',
        direction: 'short',
        quantity: 1,
        entry_price: 1.2700,
        exit_price: 1.2650,
        profit: 50,
        result: 'win',
        setup: 'Reversal Pattern',
        session: 'London',
        market: 'Forex',
        entry: 1.2700,
        exit: 1.2650,
        pnl: 50,
        rMultiple: 1.2,
        win: true,
        timestamp: new Date('2024-01-14T14:15:00Z').toISOString()
      },
      {
        id: 3,
        symbol: 'USDJPY',
        date: '2024-01-13',
        direction: 'long',
        quantity: 1,
        entry_price: 148.50,
        exit_price: 148.00,
        profit: -50,
        result: 'loss',
        setup: 'Failed Breakout',
        session: 'NY PM',
        market: 'Forex',
        entry: 148.50,
        exit: 148.00,
        pnl: -50,
        rMultiple: -0.8,
        win: false,
        timestamp: new Date('2024-01-13T16:45:00Z').toISOString()
      },
      {
        id: 4,
        symbol: 'AUDUSD',
        date: '2024-01-12',
        direction: 'short',
        quantity: 1,
        entry_price: 0.6750,
        exit_price: 0.6720,
        profit: 30,
        result: 'win',
        setup: 'Trend Continuation',
        session: 'London',
        market: 'Forex',
        entry: 0.6750,
        exit: 0.6720,
        pnl: 30,
        rMultiple: 0.9,
        win: true,
        timestamp: new Date('2024-01-12T11:20:00Z').toISOString()
      },
      {
        id: 5,
        symbol: 'USDCAD',
        date: '2024-01-11',
        direction: 'long',
        quantity: 1,
        entry_price: 1.3450,
        exit_price: 1.3480,
        profit: 30,
        result: 'win',
        setup: 'Support Bounce',
        session: 'NY AM',
        market: 'Forex',
        entry: 1.3450,
        exit: 1.3480,
        pnl: 30,
        rMultiple: 1.1,
        win: true,
        timestamp: new Date('2024-01-11T10:05:00Z').toISOString()
      }
    ];

    try {
      const transaction = this.db.transaction(['trades'], 'readwrite');
      const store = transaction.objectStore('trades');
      
      for (const trade of testTrades) {
        await new Promise((resolve, reject) => {
          const request = store.add(trade);
          request.onsuccess = () => {
            console.log(`✅ Added trade: ${trade.symbol} (${trade.date})`);
            resolve();
          };
          request.onerror = () => {
            console.log(`⚠️  Trade ${trade.symbol} might already exist, trying put...`);
            const putRequest = store.put(trade);
            putRequest.onsuccess = () => {
              console.log(`✅ Updated trade: ${trade.symbol} (${trade.date})`);
              resolve();
            };
            putRequest.onerror = () => reject(putRequest.error);
          };
        });
      }
      
      console.log('✅ All test trades added successfully!');
      return true;
    } catch (error) {
      console.error('❌ Error adding test trades:', error);
      return false;
    }
  }

  async clearAllData() {
    console.log('\n🗑️  Clearing all data...');
    
    for (const storeName of STORES) {
      if (this.db.objectStoreNames.contains(storeName)) {
        const transaction = this.db.transaction([storeName], 'readwrite');
        const store = transaction.objectStore(storeName);
        await new Promise((resolve, reject) => {
          const request = store.clear();
          request.onsuccess = () => {
            console.log(`✅ Cleared ${storeName}`);
            resolve();
          };
          request.onerror = () => reject(request.error);
        });
      }
    }
  }

  close() {
    if (this.db) {
      this.db.close();
      console.log('📪 Database connection closed');
    }
  }
}

// Main execution function
async function debugAndPopulateData() {
  const debugger = new IndexedDBDebugger();
  
  try {
    // Initialize database connection
    await debugger.init();
    
    // Check current state
    const currentState = await debugger.checkDataState();
    
    // If trades store is empty, add test data
    if (currentState.trades && currentState.trades.count === 0) {
      console.log('\n📝 Database is empty, adding test data...');
      await debugger.addTestTrades();
      
      // Verify data was added
      console.log('\n🔍 Verifying data insertion...');
      await debugger.checkDataState();
    } else {
      console.log('\n✅ Database already contains trade data');
    }
    
    // Trigger application data refresh
    console.log('\n🔄 Triggering application data refresh...');
    
    // Try to trigger a refresh in the React app
    if (window.location) {
      console.log('💡 Refreshing page to reload data...');
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
    
  } catch (error) {
    console.error('❌ Debug process failed:', error);
  } finally {
    debugger.close();
  }
}

// Make functions available globally for manual execution
window.debugIndexedDB = {
  debugAndPopulateData,
  IndexedDBDebugger
};

// Auto-run the debug process
debugAndPopulateData();
