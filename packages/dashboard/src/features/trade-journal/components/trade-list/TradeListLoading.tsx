/**
 * Trade List Loading Component
 *
 * Displays a loading state for the trade list
 */

import React from 'react';
import styled, { keyframes } from 'styled-components';
// Removed unused imports - will be added back when needed for real data integration

const shimmer = keyframes`
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
  padding: ${({ theme }) => theme.spacing.md} 0;
`;

const LoadingRow = styled.div`
  height: 60px;
  background: linear-gradient(
    to right,
    ${({ theme }) => theme.colors.background} 8%,
    ${({ theme }) => theme.colors.cardBackground} 18%,
    ${({ theme }) => theme.colors.background} 33%
  );
  background-size: 2000px 100%;
  animation: ${shimmer} 1.5s infinite linear;
  border-radius: ${({ theme }) => theme.borderRadius.sm};
`;

interface TradeListLoadingProps {
  rowCount?: number;
}

/**
 * Trade List Loading Component
 */
const TradeListLoading: React.FC<TradeListLoadingProps> = ({ rowCount = 5 }) => {
  return (
    <LoadingContainer>
      {Array.from({ length: rowCount }).map((_, index) => (
        <LoadingRow key={index} />
      ))}
    </LoadingContainer>
  );
};

export default TradeListLoading;
