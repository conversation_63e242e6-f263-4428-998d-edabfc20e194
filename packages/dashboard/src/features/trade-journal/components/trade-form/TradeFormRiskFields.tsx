/**
 * Trade Form Risk Fields Component
 *
 * Displays the risk management fields for the trade form
 */

import React from 'react';
import styled from 'styled-components';
// Removed unused imports - will be added back when needed for real data integration
import { TradeFormValues } from '../../types';
import { ValidationErrors } from '../../hooks/useTradeValidation';

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.xs};
`;

const Label = styled.label`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const Input = styled.input`
  padding: ${({ theme }) => theme.spacing.sm};
  background-color: ${({ theme }) => theme.colors.background};
  border: 1px solid ${({ theme }) => theme.colors.border};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  color: ${({ theme }) => theme.colors.textPrimary};

  &:focus {
    border-color: ${({ theme }) => theme.colors.primary};
    outline: none;
  }
`;

const HelpText = styled.span`
  font-size: 0.8rem;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

interface TradeFormRiskFieldsProps {
  formValues: TradeFormValues;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
  validationErrors: ValidationErrors;
}

/**
 * Trade Form Risk Fields Component
 */
const TradeFormRiskFields: React.FC<TradeFormRiskFieldsProps> = ({
  formValues,
  handleChange,
  validationErrors,
}) => {
  return (
    <>
      <FormRow>
        <FormGroup>
          <Label htmlFor="stopLoss">Stop Loss</Label>
          <Input
            id="stopLoss"
            name="stopLoss"
            type="number"
            step="0.01"
            value={formValues.stopLoss || ''}
            onChange={handleChange}
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="takeProfit">Take Profit</Label>
          <Input
            id="takeProfit"
            name="takeProfit"
            type="number"
            step="0.01"
            value={formValues.takeProfit || ''}
            onChange={handleChange}
          />
        </FormGroup>
      </FormRow>

      <FormRow>
        <FormGroup>
          <Label htmlFor="riskPoints">Risk (Points)</Label>
          <Input
            id="riskPoints"
            name="riskPoints"
            type="number"
            step="0.01"
            value={formValues.riskPoints || ''}
            onChange={handleChange}
          />
        </FormGroup>

        <FormGroup>
          <Label htmlFor="rMultiple">R-Multiple</Label>
          <Input
            id="rMultiple"
            name="rMultiple"
            type="number"
            step="0.01"
            value={formValues.rMultiple || ''}
            onChange={handleChange}
            disabled
          />
          <HelpText>Auto-calculated from Risk Points and P/L</HelpText>
        </FormGroup>
      </FormRow>
    </>
  );
};

export default TradeFormRiskFields;
