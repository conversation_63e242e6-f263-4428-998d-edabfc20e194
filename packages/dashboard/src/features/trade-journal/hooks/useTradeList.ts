/**
 * useTradeList Hook
 *
 * Custom hook for managing trade list state and behavior
 */

import { useState, useMemo } from 'react';
// Removed unused imports - will be added back when needed for real data integration
import { CompleteTradeData } from '../types';

/**
 * Hook for managing trade list state and behavior
 * @param trades The trades to display
 * @param expandable Whether the rows can be expanded
 */
export function useTradeList(trades: CompleteTradeData[], expandable: boolean = false) {
  // Track expanded rows
  const [expandedRows, setExpandedRows] = useState<Record<number, boolean>>({});

  // Toggle row expansion
  const toggleRowExpansion = (tradeId: number) => {
    if (!expandable) return;

    setExpandedRows((prev) => ({
      ...prev,
      [tradeId]: !prev[tradeId],
    }));
  };

  // Check if a row is expanded
  const isRowExpanded = (tradeId: number) => {
    return expandable && expandedRows[tradeId];
  };

  // Sort trades by date (newest first)
  const sortedTrades = useMemo(() => {
    if (!trades) return [];

    return [...trades].sort((a, b) => {
      const dateA = new Date(a.trade.date).getTime();
      const dateB = new Date(b.trade.date).getTime();
      return dateB - dateA; // Newest first
    });
  }, [trades]);

  return {
    sortedTrades,
    expandedRows,
    toggleRowExpansion,
    isRowExpanded,
  };
}

export type TradeListHook = ReturnType<typeof useTradeList>;
