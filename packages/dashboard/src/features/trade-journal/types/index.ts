/**
 * Trade Journal Types
 */

import { Trade } from '@adhd-trading-dashboard/shared';

/**
 * Model Type options for actual trading models (not AI-generated generic ones)
 */
export type ModelType = 'RD-Cont' | 'FVG-RD' | 'Combined';

/**
 * Session (Time Block) options
 */
export type SessionType =
  | 'Pre-Market'
  | 'Regular Hours'
  | 'Power Hour'
  | 'After Hours'
  | 'Overnight';

/**
 * Setup types - now handled by Setup Construction Matrix (modular approach)
 * Legacy type kept for backward compatibility but not used in new forms
 */
export type SetupType = string; // Replaced by SetupComponents interface

/**
 * Market types
 */
export type MarketType = 'Stocks' | 'Options' | 'Futures' | 'Forex' | 'Crypto' | 'Other';

/**
 * Entry version types
 */
export type EntryVersionType =
  | 'First Entry'
  | 'Re-Entry'
  | 'Scale In'
  | 'Averaging Down'
  | 'Averaging Up';

/**
 * Setup Category types for classification
 */
export type SetupCategoryType = 'structure' | 'session' | 'model';

/**
 * Structure-Based Setup types
 */
export type StructureSetupType =
  | 'High/Low Reversal Setup'
  | 'FVG Redelivery Setup'
  | 'Strong-FVG Reversal Setup'
  | 'NWOG Reaction Setup'
  | 'NDOG Reaction Setup'
  | 'Multi-Array Confluence Setup';

/**
 * Session-Based Setup types
 */
export type SessionSetupType =
  | 'Opening Range Setup (09:30-10:10)'
  | 'Morning Session Setup (10:50-11:10)'
  | 'Lunch Macro Setup (11:45-13:15)'
  | 'Afternoon Session Setup (13:30-15:00)'
  | 'MOC Setup (15:30-16:15)';

/**
 * Model-Specific Setup types
 */
export type ModelSetupType =
  | 'Simple FVG-RD'
  | 'Complex FVG-RD'
  | 'True-RD Continuation'
  | 'IMM-RD Continuation'
  | 'Dispersed-RD Continuation'
  | 'Wide-Gap-RD Continuation';

/**
 * Liquidity types
 */
export type LiquidityType =
  | 'London-H/L'
  | 'Premarket-H/L'
  | '09:30-Opening-Range-H/L'
  | 'Post-10:00am-Lunch-Macro-H/L'
  | 'Lunch-H/L'
  | 'Monthly-H/L'
  | 'Prev-Week-H/L'
  | 'Prev-Day-H/L'
  | 'Macro-H/L';

/**
 * FVG types
 */
export type FVGType =
  | 'Monthly-FVG'
  | 'Weekly-FVG'
  | 'Daily-FVG'
  | 'Daily-Top/Bottom-FVG'
  | '1h-Top/Bottom-FVG'
  | '15min-Top/Bottom-FVG'
  | 'MNOR-FVG'
  | 'Asia-FPFVG'
  | 'Premarket-FPFVG'
  | 'AM-FPFVG'
  | 'PM-FPFVG'
  | 'Prev-Day-MNOR-FVG'
  | 'Prev-Day-Asia-FPFVG'
  | 'Prev-Day-Premarket-FPFVG'
  | 'Prev-Day-AM-FPFVG'
  | 'Prev-Day-PM-FPFVG'
  | '3Day-MNOR-FVG'
  | '3Day-Asia-FPFVG'
  | '3Day-Premarket-FPFVG'
  | '3Day-AM-FPFVG'
  | '3Day-PM-FPFVG'
  | 'Top/Bottom-FVG'
  | 'Macro-FVG'
  | 'News-FVG'
  | '10min-Prior-To-News-FVG'
  | 'Strong-FVG'
  | 'RDRB-FVG';

/**
 * DOL Target types
 */
export type DOLTargetType = 'FVG Target' | 'Liquidity Target' | 'RD Target';

/**
 * Parent PD Array types
 */
export type ParentPDArrayType =
  | 'NWOG'
  | 'Old-NWOG'
  | 'NDOG'
  | 'Old-NDOG'
  | 'Monthly-FVG'
  | 'Weekly-FVG'
  | 'Daily-FVG'
  | 'Daily-Top/Bottom-FVG'
  | '1h-Top/Bottom-FVG'
  | '15min-Top/Bottom-FVG';

/**
 * Pattern Quality Assessment Score Range
 */
export type ScoreRange = 'excellent' | 'good' | 'average' | 'poor' | 'unacceptable';

/**
 * Pattern Quality Assessment Criteria
 */
export interface PatternQualityCriteria {
  clarity: ScoreRange;
  confluence: ScoreRange;
  context: ScoreRange;
  risk: ScoreRange;
  reward: ScoreRange;
  timeframe: ScoreRange;
  volume: ScoreRange;
}

/**
 * Pattern Quality Assessment Score
 */
export interface PatternQualityScore {
  total: number;
  rating: number; // 1-10 rating
  criteria: PatternQualityCriteria;
  notes: string;
}

/**
 * DOL (Draw on Liquidity) Type
 */
export type DOLType = 'Sweep' | 'Tap' | 'Approach' | 'Rejection';

/**
 * DOL (Draw on Liquidity) Strength
 */
export type DOLStrength = 'Strong' | 'Moderate' | 'Weak';

/**
 * DOL (Draw on Liquidity) Reaction
 */
export type DOLReaction =
  | 'Immediate Reversal'
  | 'Delayed Reversal'
  | 'Consolidation'
  | 'Continuation';

/**
 * DOL (Draw on Liquidity) Context
 */
export type DOLContext =
  | 'High Volume Node'
  | 'Low Volume Node'
  | 'VPOC'
  | 'VAH/VAL'
  | 'Previous Day High/Low'
  | 'Previous Week High/Low'
  | 'Previous Month High/Low'
  | 'Round Number'
  | 'Technical Level'
  | 'News Event'
  | 'Other';

/**
 * DOL (Draw on Liquidity) Analysis
 */
export interface DOLAnalysis {
  // Basic DOL Information
  dolType: DOLType;
  dolStrength: DOLStrength;
  dolReaction: DOLReaction;
  dolContext: DOLContext[];

  // Detailed Analysis
  priceAction: string;
  volumeProfile: string;
  timeOfDay: string;
  marketStructure: string;

  // Outcome Analysis
  effectiveness: number; // 1-10 rating
  notes: string;
}

// Import centralized types instead of defining locally
export type {
  Trade,
  TradeFormData as TradeFormValues, // Alias for backward compatibility
  TradeFvgDetails,
  TradeSetup,
  TradeAnalysis,
  CompleteTradeData,
  TradeFilters,
  PerformanceMetrics,
  Position,
  Order,
  MarketData,
  TradingSession,
} from '@adhd-trading-dashboard/shared';

// TradeFormValues is now imported as TradeFormData from centralized types

export interface TradeJournalState {
  trades: Trade[];
  isLoading: boolean;
  error: string | null;
  refreshTrades?: () => Promise<void>;
}

/**
 * Filter state for trade journal
 */
export interface FilterState {
  symbol: string;
  direction: string;
  setup: string;
  modelType: string;
  result: string;
  dateFrom: string;
  dateTo: string;
  primarySetupType: string;
  secondarySetupType: string;
  liquidityTaken: string;
  patternQualityMin: string;
  patternQualityMax: string;
  dolType: string;
  dolEffectivenessMin: string;
  dolEffectivenessMax: string;
}
