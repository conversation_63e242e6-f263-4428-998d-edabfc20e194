/**
 * Recent Trades Table Component
 *
 * Displays a table of recent trades
 */

import React, { useState, useMemo, useCallback } from 'react';
import styled from 'styled-components';
import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';

interface RecentTradesTableProps {
  trades: Trade[];
  isLoading?: boolean;
}

type SortField =
  | 'date'
  | 'setup'
  | 'session'
  | 'direction'
  | 'market'
  | 'entry'
  | 'exit'
  | 'rMultiple'
  | 'pnl';
type SortDirection = 'asc' | 'desc';

const TableContainer = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
  overflow-x: auto;
  margin-bottom: ${({ theme }) => theme.spacing.lg};
`;

const TableTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
`;

const TableHead = styled.thead`
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const TableHeader = styled.th<{ sortable?: boolean; active?: boolean }>`
  text-align: left;
  padding: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  text-transform: uppercase;
  cursor: ${({ sortable }) => (sortable ? 'pointer' : 'default')};
  user-select: none;
  position: relative;

  ${({ sortable, theme }) =>
    sortable &&
    `
    &:hover {
      color: ${theme.colors.textPrimary};
      background-color: rgba(255, 255, 255, 0.05);
    }
  `}

  ${({ active, theme }) =>
    active &&
    `
    color: ${theme.colors.primary};
    font-weight: 600;
  `}
`;

const SortIcon = styled.span<{ direction?: SortDirection }>`
  margin-left: 4px;
  font-size: 12px;
  opacity: 0.7;

  ${({ direction }) => {
    if (direction === 'asc') return 'content: "↑";';
    if (direction === 'desc') return 'content: "↓";';
    return 'content: "↕"; opacity: 0.3;';
  }}

  &::after {
    ${({ direction }) => {
      if (direction === 'asc') return 'content: "↑";';
      if (direction === 'desc') return 'content: "↓";';
      return 'content: "↕";';
    }}
  }
`;

const TableRow = styled.tr`
  border-bottom: 1px solid #4b5563;

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
`;

const TableCell = styled.td`
  padding: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const DirectionCell = styled(TableCell)<{ direction: 'Long' | 'Short' }>`
  color: ${({ theme, direction }) =>
    direction === 'Long' ? theme.colors.success : theme.colors.error};
`;

const ResultCell = styled(TableCell)<{ win: boolean }>`
  color: ${({ theme, win }) => (win ? theme.colors.success : theme.colors.error)};
`;

const PnlCell = styled(TableCell)<{ value: number }>`
  color: ${({ theme, value }) => (value >= 0 ? theme.colors.success : theme.colors.error)};
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const NoDataContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

/**
 * Debug function for comprehensive trade sorting analysis
 */
const debugTradesSorting = (trades: Trade[], sortBy: SortField, sortOrder: SortDirection) => {
  console.group('🔍 Recent Trades Sorting Debug');

  // Log raw data
  console.log('📊 Raw trades count:', trades.length);
  console.log('📊 Sort criteria:', { sortBy, sortOrder });

  // Log first few trades to understand structure
  console.log('📋 Sample raw trades (first 3):');
  trades.slice(0, 3).forEach((trade, index) => {
    console.log(`Trade ${index + 1}:`, {
      id: trade.id,
      symbol: trade.symbol,
      date: trade.date,
      direction: trade.direction,
      entry: trade.entry,
      exit: trade.exit,
      // Log the field we're sorting by
      [sortBy]: trade[sortBy as keyof Trade],
    });
  });

  console.groupEnd();
};

/**
 * RecentTradesTable Component
 *
 * Displays a table of recent trades with sorting functionality
 */
export const RecentTradesTable: React.FC<RecentTradesTableProps> = ({
  trades,
  isLoading = false,
}) => {
  const [sortField, setSortField] = useState<SortField>('date');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');

  // Debug incoming trades data
  React.useEffect(() => {
    if (trades && trades.length > 0) {
      console.log('🎯 RecentTradesTable received trades:', {
        count: trades.length,
        firstTrade: {
          id: trades[0].id,
          date: trades[0].date,
          symbol: trades[0].symbol,
        },
        lastTrade: {
          id: trades[trades.length - 1].id,
          date: trades[trades.length - 1].date,
          symbol: trades[trades.length - 1].symbol,
        },
      });
    }
  }, [trades]);

  // Handle column sorting
  const handleSort = useCallback(
    (field: SortField) => {
      console.log(`🔄 Sorting by ${field}`);

      if (sortField === field) {
        // Toggle direction if same field
        const newDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        setSortDirection(newDirection);
        console.log(`🔄 Toggled direction to ${newDirection}`);
      } else {
        // Set new field and default direction
        setSortField(field);
        setSortDirection('desc');
        console.log(`🔄 New field ${field} with desc direction`);
      }
    },
    [sortField, sortDirection]
  );

  // Sort trades with comprehensive debugging
  const sortedTrades = useMemo(() => {
    if (!trades || trades.length === 0) return [];

    debugTradesSorting(trades, sortField, sortDirection);

    const sorted = [...trades].sort((a, b) => {
      let aValue: any, bValue: any;

      // Extract values based on sortField
      switch (sortField) {
        case 'date':
          aValue = new Date(a.date || '');
          bValue = new Date(b.date || '');
          break;
        case 'setup':
          aValue = (a as any).setup || '';
          bValue = (b as any).setup || '';
          break;
        case 'session':
          aValue = (a as any).session || '';
          bValue = (b as any).session || '';
          break;
        case 'direction':
          aValue = a.direction?.toLowerCase() || '';
          bValue = b.direction?.toLowerCase() || '';
          break;
        case 'market':
          aValue = (a as any).market || '';
          bValue = (b as any).market || '';
          break;
        case 'entry':
          aValue = parseFloat(String(a.entry || 0));
          bValue = parseFloat(String(b.entry || 0));
          break;
        case 'exit':
          aValue = parseFloat(String(a.exit || 0));
          bValue = parseFloat(String(b.exit || 0));
          break;
        case 'rMultiple':
          aValue = parseFloat(String((a as any).rMultiple || 0));
          bValue = parseFloat(String((b as any).rMultiple || 0));
          break;
        case 'pnl':
          aValue = parseFloat(String((a as any).pnl || 0));
          bValue = parseFloat(String((b as any).pnl || 0));
          break;
        default:
          aValue = a[sortField as keyof Trade];
          bValue = b[sortField as keyof Trade];
      }

      console.log(`🔄 Comparing: ${aValue} vs ${bValue} (${sortField})`);

      // Handle different data types
      if (aValue instanceof Date && bValue instanceof Date) {
        const comparison = aValue.getTime() - bValue.getTime();
        return sortDirection === 'asc' ? comparison : -comparison;
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        const comparison = aValue - bValue;
        return sortDirection === 'asc' ? comparison : -comparison;
      }

      // String comparison
      const comparison = String(aValue).localeCompare(String(bValue));
      return sortDirection === 'asc' ? comparison : -comparison;
    });

    // Log sorted results
    console.log('✅ Sorted trades (first 3):');
    sorted.slice(0, 3).forEach((trade, index) => {
      console.log(`Sorted ${index + 1}:`, {
        id: trade.id,
        date: trade.date,
        [sortField]: trade[sortField as keyof Trade],
      });
    });

    return sorted;
  }, [trades, sortField, sortDirection]);
  if (isLoading) {
    return (
      <TableContainer>
        <TableTitle>Recent Trades</TableTitle>
        <LoadingContainer>Loading trades data...</LoadingContainer>
      </TableContainer>
    );
  }

  if (!trades || trades.length === 0) {
    return (
      <TableContainer>
        <TableTitle>Recent Trades</TableTitle>
        <NoDataContainer>No trades data available</NoDataContainer>
      </TableContainer>
    );
  }

  return (
    <TableContainer>
      <TableTitle>Recent Trades ({sortedTrades.length})</TableTitle>
      <Table>
        <TableHead>
          <tr>
            <TableHeader sortable active={sortField === 'date'} onClick={() => handleSort('date')}>
              Date
              <SortIcon direction={sortField === 'date' ? sortDirection : undefined} />
            </TableHeader>
            <TableHeader
              sortable
              active={sortField === 'setup'}
              onClick={() => handleSort('setup')}
            >
              Setup
              <SortIcon direction={sortField === 'setup' ? sortDirection : undefined} />
            </TableHeader>
            <TableHeader
              sortable
              active={sortField === 'session'}
              onClick={() => handleSort('session')}
            >
              Session
              <SortIcon direction={sortField === 'session' ? sortDirection : undefined} />
            </TableHeader>
            <TableHeader
              sortable
              active={sortField === 'direction'}
              onClick={() => handleSort('direction')}
            >
              Direction
              <SortIcon direction={sortField === 'direction' ? sortDirection : undefined} />
            </TableHeader>
            <TableHeader
              sortable
              active={sortField === 'market'}
              onClick={() => handleSort('market')}
            >
              Market
              <SortIcon direction={sortField === 'market' ? sortDirection : undefined} />
            </TableHeader>
            <TableHeader
              sortable
              active={sortField === 'entry'}
              onClick={() => handleSort('entry')}
            >
              Entry
              <SortIcon direction={sortField === 'entry' ? sortDirection : undefined} />
            </TableHeader>
            <TableHeader sortable active={sortField === 'exit'} onClick={() => handleSort('exit')}>
              Exit
              <SortIcon direction={sortField === 'exit' ? sortDirection : undefined} />
            </TableHeader>
            <TableHeader
              sortable
              active={sortField === 'rMultiple'}
              onClick={() => handleSort('rMultiple')}
            >
              R-Multiple
              <SortIcon direction={sortField === 'rMultiple' ? sortDirection : undefined} />
            </TableHeader>
            <TableHeader sortable active={sortField === 'pnl'} onClick={() => handleSort('pnl')}>
              P&L
              <SortIcon direction={sortField === 'pnl' ? sortDirection : undefined} />
            </TableHeader>
          </tr>
        </TableHead>
        <tbody>
          {sortedTrades.map((trade) => (
            <TableRow key={trade.id}>
              <TableCell>{trade.date}</TableCell>
              <TableCell>{trade.setup}</TableCell>
              <TableCell>{trade.session}</TableCell>
              <DirectionCell direction={trade.direction}>{trade.direction}</DirectionCell>
              <TableCell>{trade.market}</TableCell>
              <TableCell>{trade.entry}</TableCell>
              <TableCell>{trade.exit}</TableCell>
              <ResultCell win={trade.win}>{trade.rMultiple.toFixed(2)}</ResultCell>
              <PnlCell value={trade.pnl}>${trade.pnl.toFixed(2)}</PnlCell>
            </TableRow>
          ))}
        </tbody>
      </Table>
    </TableContainer>
  );
};

export default RecentTradesTable;
