/**
 * Setup Analysis Component
 *
 * Displays performance metrics for different trading setups
 */

import React from 'react';
import styled from 'styled-components';
// Removed unused imports - will be added back when needed for real data integration
import { SetupPerformance, SessionPerformance } from '../types';

interface SetupAnalysisProps {
  setupPerformance: SetupPerformance[];
  sessionPerformance: SessionPerformance[];
  isLoading?: boolean;
}

const Container = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${({ theme }) => theme.spacing.lg};
  margin-bottom: ${({ theme }) => theme.spacing.lg};

  @media (max-width: ${({ theme }) => theme.breakpoints.md}) {
    grid-template-columns: 1fr;
  }
`;

const AnalysisCard = styled.div`
  background-color: ${({ theme }) => theme.colors.surface};
  border-radius: ${({ theme }) => theme.borderRadius.md};
  padding: ${({ theme }) => theme.spacing.md};
  box-shadow: ${({ theme }) => theme.shadows.sm};
`;

const CardTitle = styled.h3`
  font-size: ${({ theme }) => theme.fontSizes.md};
  color: ${({ theme }) => theme.colors.textPrimary};
  margin: 0 0 ${({ theme }) => theme.spacing.md} 0;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
`;

const TableHead = styled.thead`
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};
`;

const TableHeader = styled.th`
  text-align: left;
  padding: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
  font-weight: 500;
  text-transform: uppercase;
`;

const TableRow = styled.tr`
  border-bottom: 1px solid ${({ theme }) => theme.colors.border};

  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
  }
`;

const TableCell = styled.td`
  padding: ${({ theme }) => theme.spacing.sm};
  color: ${({ theme }) => theme.colors.textPrimary};
  font-size: ${({ theme }) => theme.fontSizes.sm};
`;

const WinRateCell = styled(TableCell)<{ value: number }>`
  color: ${({ theme, value }) => {
    if (value >= 70) return theme.colors.success;
    if (value >= 50) return theme.colors.warning;
    return theme.colors.error;
  }};
`;

const PnlCell = styled(TableCell)<{ value: number }>`
  color: ${({ theme, value }) => (value >= 0 ? theme.colors.success : theme.colors.error)};
`;

const LoadingContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

const NoDataContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: ${({ theme }) => theme.colors.textSecondary};
`;

/**
 * SetupAnalysis Component
 *
 * Displays performance metrics for different trading setups
 */
export const SetupAnalysis: React.FC<SetupAnalysisProps> = ({
  setupPerformance,
  sessionPerformance,
  isLoading = false,
}) => {
  if (isLoading) {
    return (
      <Container>
        <AnalysisCard>
          <CardTitle>Setup Performance</CardTitle>
          <LoadingContainer>Loading setup data...</LoadingContainer>
        </AnalysisCard>
        <AnalysisCard>
          <CardTitle>Session Performance</CardTitle>
          <LoadingContainer>Loading session data...</LoadingContainer>
        </AnalysisCard>
      </Container>
    );
  }

  return (
    <Container>
      <AnalysisCard>
        <CardTitle>Setup Performance</CardTitle>
        {setupPerformance.length === 0 ? (
          <NoDataContainer>No setup data available</NoDataContainer>
        ) : (
          <Table>
            <TableHead>
              <tr>
                <TableHeader>Setup</TableHeader>
                <TableHeader>Win Rate</TableHeader>
                <TableHeader>Avg R</TableHeader>
                <TableHeader>Trades</TableHeader>
                <TableHeader>P&L</TableHeader>
              </tr>
            </TableHead>
            <tbody>
              {setupPerformance.map((setup, index) => (
                <TableRow key={index}>
                  <TableCell>{setup.name}</TableCell>
                  <WinRateCell value={setup.winRate}>{setup.winRate.toFixed(1)}%</WinRateCell>
                  <TableCell>{setup.avgRMultiple.toFixed(2)}</TableCell>
                  <TableCell>{setup.totalTrades}</TableCell>
                  <PnlCell value={setup.pnl}>${setup.pnl.toFixed(2)}</PnlCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        )}
      </AnalysisCard>

      <AnalysisCard>
        <CardTitle>Session Performance</CardTitle>
        {sessionPerformance.length === 0 ? (
          <NoDataContainer>No session data available</NoDataContainer>
        ) : (
          <Table>
            <TableHead>
              <tr>
                <TableHeader>Session</TableHeader>
                <TableHeader>Win Rate</TableHeader>
                <TableHeader>Avg R</TableHeader>
                <TableHeader>Trades</TableHeader>
                <TableHeader>P&L</TableHeader>
              </tr>
            </TableHead>
            <tbody>
              {sessionPerformance.map((session, index) => (
                <TableRow key={index}>
                  <TableCell>{session.name}</TableCell>
                  <WinRateCell value={session.winRate}>{session.winRate.toFixed(1)}%</WinRateCell>
                  <TableCell>{session.avgRMultiple.toFixed(2)}</TableCell>
                  <TableCell>{session.totalTrades}</TableCell>
                  <PnlCell value={session.pnl}>${session.pnl.toFixed(2)}</PnlCell>
                </TableRow>
              ))}
            </tbody>
          </Table>
        )}
      </AnalysisCard>
    </Container>
  );
};

export default SetupAnalysis;
