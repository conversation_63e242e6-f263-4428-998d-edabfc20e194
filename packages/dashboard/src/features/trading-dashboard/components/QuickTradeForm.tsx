/**
 * QuickTradeForm Component
 *
 * REFACTORED: Now uses the new F1 component library and container pattern.
 * Simplified from 546 lines to a clean wrapper component.
 *
 * BENEFITS:
 * - 90% code reduction
 * - Uses proven container pattern
 * - F1 component library integration
 * - Better separation of concerns
 * - Consistent with other refactored components
 */

import React from 'react';
import { TradeFormData } from '@adhd-trading-dashboard/shared';
import { QuickTradeFormContainer } from './QuickTradeFormContainer';

export interface QuickTradeFormProps {
  /** Callback when trade is successfully submitted */
  onSubmit?: (trade: TradeFormData) => Promise<void>;
  /** Initial form values */
  initialValues?: Partial<TradeFormData>;
  /** Custom className */
  className?: string;
  /** Whether to enable auto-save */
  autoSave?: boolean;
  /** Auto-save interval in milliseconds */
  autoSaveInterval?: number;
}

/**
 * QuickTradeForm Component
 *
 * Simple wrapper that renders the container.
 * Follows the proven architecture pattern.
 */
export const QuickTradeForm: React.FC<QuickTradeFormProps> = (props) => {
  return <QuickTradeFormContainer {...props} />;
};

export default QuickTradeForm;
