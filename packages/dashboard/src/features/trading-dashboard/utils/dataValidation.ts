/**
 * Data Validation Utilities
 *
 * EXTRACTED FROM: Data transformation logic for better error handling
 * Provides data validation, sanitization, and fallback mechanisms.
 *
 * BENEFITS:
 * - Prevents crashes from invalid data
 * - Provides meaningful error messages
 * - Ensures data consistency
 * - Supports graceful degradation
 */

import { Trade, CompleteTradeData } from '@adhd-trading-dashboard/shared';

/**
 * Validation error types
 */
export interface ValidationError {
  field: string;
  value: any;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
  sanitizedData?: any;
}

/**
 * Data validation utilities
 */
export const DataValidators = {
  /**
   * Validate a single trade object
   */
  validateTrade: (trade: any, index: number = 0): ValidationResult => {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // Required fields validation
    if (!trade) {
      errors.push({
        field: 'trade',
        value: trade,
        message: `Trade object is null or undefined at index ${index}`,
        severity: 'error',
      });
      return { isValid: false, errors, warnings };
    }

    // ID validation
    if (!trade.id && trade.id !== 0) {
      warnings.push({
        field: 'id',
        value: trade.id,
        message: `Trade ID is missing at index ${index}, will use index as fallback`,
        severity: 'warning',
      });
    }

    // Date validation
    if (!trade.date) {
      warnings.push({
        field: 'date',
        value: trade.date,
        message: `Trade date is missing at index ${index}, will use current date`,
        severity: 'warning',
      });
    } else {
      const dateObj = new Date(trade.date);
      if (isNaN(dateObj.getTime())) {
        errors.push({
          field: 'date',
          value: trade.date,
          message: `Invalid date format: ${trade.date} at index ${index}`,
          severity: 'error',
        });
      }
    }

    // Numeric field validation
    const numericFields = [
      'entry_price',
      'exit_price',
      'r_multiple',
      'pattern_quality_rating',
      'risk_points',
      'achieved_pl',
    ];

    numericFields.forEach((field) => {
      const value = trade[field];
      if (value !== null && value !== undefined && value !== '') {
        const numValue = Number(value);
        if (isNaN(numValue)) {
          warnings.push({
            field,
            value,
            message: `Invalid numeric value for ${field}: ${value} at index ${index}`,
            severity: 'warning',
          });
        }
      }
    });

    // Direction validation
    if (trade.direction && !['Long', 'Short'].includes(trade.direction)) {
      warnings.push({
        field: 'direction',
        value: trade.direction,
        message: `Invalid direction: ${trade.direction} at index ${index}, expected 'Long' or 'Short'`,
        severity: 'warning',
      });
    }

    // Win/Loss validation
    if (trade.win_loss && !['Win', 'Loss'].includes(trade.win_loss)) {
      warnings.push({
        field: 'win_loss',
        value: trade.win_loss,
        message: `Invalid win_loss value: ${trade.win_loss} at index ${index}, expected 'Win' or 'Loss'`,
        severity: 'warning',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  },

  /**
   * Validate an array of trades
   */
  validateTrades: (trades: any[]): ValidationResult => {
    if (!Array.isArray(trades)) {
      return {
        isValid: false,
        errors: [
          {
            field: 'trades',
            value: trades,
            message: 'Trades data is not an array',
            severity: 'error',
          },
        ],
        warnings: [],
      };
    }

    const allErrors: ValidationError[] = [];
    const allWarnings: ValidationError[] = [];

    trades.forEach((trade, index) => {
      const result = DataValidators.validateTrade(trade, index);
      allErrors.push(...result.errors);
      allWarnings.push(...result.warnings);
    });

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
    };
  },

  /**
   * Validate CompleteTradeData structure
   */
  validateCompleteTradeData: (data: any[]): ValidationResult => {
    if (!Array.isArray(data)) {
      return {
        isValid: false,
        errors: [
          {
            field: 'completeTradeData',
            value: data,
            message: 'CompleteTradeData is not an array',
            severity: 'error',
          },
        ],
        warnings: [],
      };
    }

    const allErrors: ValidationError[] = [];
    const allWarnings: ValidationError[] = [];

    data.forEach((item, index) => {
      if (!item || typeof item !== 'object') {
        allErrors.push({
          field: 'completeTradeData',
          value: item,
          message: `Invalid CompleteTradeData item at index ${index}`,
          severity: 'error',
        });
        return;
      }

      if (!item.trade) {
        allErrors.push({
          field: 'trade',
          value: item.trade,
          message: `Missing trade object in CompleteTradeData at index ${index}`,
          severity: 'error',
        });
      } else {
        const tradeResult = DataValidators.validateTrade(item.trade, index);
        allErrors.push(...tradeResult.errors);
        allWarnings.push(...tradeResult.warnings);
      }

      // Validate optional fields
      if (item.fvg_details && typeof item.fvg_details !== 'object') {
        allWarnings.push({
          field: 'fvg_details',
          value: item.fvg_details,
          message: `Invalid fvg_details structure at index ${index}`,
          severity: 'warning',
        });
      }
    });

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings,
    };
  },
};

/**
 * Data sanitization utilities
 */
export const DataSanitizers = {
  /**
   * Sanitize a trade object with fallback values
   */
  sanitizeTrade: (trade: any, index: number = 0): Trade => {
    const currentDate = new Date().toISOString().split('T')[0];

    return {
      id: trade?.id?.toString() || index.toString(),
      date: DataSanitizers.sanitizeDate(trade?.date) || currentDate,
      model: trade?.model_type || 'Unknown',
      session: trade?.session || 'Unknown',
      setup: trade?.setup || 'No setup',
      entry: DataSanitizers.sanitizeNumber(trade?.entry_price) || 0,
      exit: DataSanitizers.sanitizeNumber(trade?.exit_price) || 0,
      direction: DataSanitizers.sanitizeDirection(trade?.direction) || 'Long',
      market: trade?.market || 'MNQ',
      rMultiple: DataSanitizers.sanitizeNumber(trade?.r_multiple) || 0,
      patternQuality: DataSanitizers.sanitizeNumber(trade?.pattern_quality_rating) || 0,
      win: trade?.win_loss === 'Win',
      entryPrice: DataSanitizers.sanitizeNumber(trade?.entry_price) || 0,
      exitPrice: DataSanitizers.sanitizeNumber(trade?.exit_price) || 0,
      risk: DataSanitizers.sanitizeNumber(trade?.risk_points) || 0,
      pnl: DataSanitizers.sanitizeNumber(trade?.achieved_pl) || 0,
      dolTarget: trade?.dol_target || '',
      rdType: trade?.rd_type || '',
      entryVersion: trade?.entry_version || '',
      drawOnLiquidity: trade?.draw_on_liquidity || '',
    };
  },

  /**
   * Sanitize date string
   */
  sanitizeDate: (date: any): string | null => {
    if (!date) return null;

    try {
      const dateObj = new Date(date);
      if (isNaN(dateObj.getTime())) return null;
      return dateObj.toISOString().split('T')[0];
    } catch {
      return null;
    }
  },

  /**
   * Sanitize time string
   */
  sanitizeTime: (time: any): string | null => {
    if (!time) return null;

    // Basic time format validation (HH:MM:SS or HH:MM)
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$/;
    if (typeof time === 'string' && timeRegex.test(time)) {
      return time.includes(':') && time.split(':').length === 2 ? `${time}:00` : time;
    }

    return null;
  },

  /**
   * Sanitize direction value
   */
  sanitizeDirection: (direction: any): 'Long' | 'Short' | null => {
    if (typeof direction === 'string') {
      const normalized = direction.toLowerCase();
      if (normalized === 'long' || normalized === 'buy') return 'Long';
      if (normalized === 'short' || normalized === 'sell') return 'Short';
    }
    return null;
  },

  /**
   * Sanitize numeric value
   */
  sanitizeNumber: (value: any): number | null => {
    if (value === null || value === undefined || value === '') return null;

    const numValue = Number(value);
    return isNaN(numValue) ? null : numValue;
  },
};

/**
 * Error reporting utilities
 */
export const ErrorReporter = {
  /**
   * Log validation results
   */
  logValidationResults: (result: ValidationResult, context: string = '') => {
    if (result.errors.length > 0) {
      console.error(`Validation errors in ${context}:`, result.errors);
    }

    if (result.warnings.length > 0) {
      console.warn(`Validation warnings in ${context}:`, result.warnings);
    }

    if (result.isValid && result.warnings.length === 0) {
      console.log(`Validation passed for ${context}`);
    }
  },

  /**
   * Create user-friendly error message
   */
  createErrorMessage: (result: ValidationResult): string => {
    if (result.isValid) return '';

    const errorCount = result.errors.length;
    const warningCount = result.warnings.length;

    let message = `Data validation failed with ${errorCount} error${errorCount !== 1 ? 's' : ''}`;

    if (warningCount > 0) {
      message += ` and ${warningCount} warning${warningCount !== 1 ? 's' : ''}`;
    }

    message += '. Some data may be incomplete or invalid.';

    return message;
  },
};

export default {
  DataValidators,
  DataSanitizers,
  ErrorReporter,
};
