/**
 * Trade Analysis Feature
 *
 * This module exports components and hooks for the trade analysis feature.
 */

import { Trade, TradeFormData } from '@adhd-trading-dashboard/shared';

// Main component
export { default as TradeAnalysis } from './TradeAnalysis';
export {
  TradeAnalysisProvider as LegacyTradeAnalysisProvider,
  useTradeAnalysis as useLegacyTradeAnalysis,
} from './hooks/TradeAnalysisContext';
export { PerformanceSummary } from './components/PerformanceSummary';
export { TradesTable } from './components/TradesTable';
export { CategoryPerformanceChart } from './components/CategoryPerformanceChart';
export { TimePerformanceChart } from './components/TimePerformanceChart';
export { FilterPanel } from './components/FilterPanel';
export { TradeDetail } from './components/TradeDetail';

// New components
export * from './components/TradeAnalysisTable';
export * from './components/TradeAnalysisSummary';
export * from './components/TradeAnalysisCharts';

// New hooks
export * from './hooks/useTradeAnalysis';

// Types
export * from './types';
