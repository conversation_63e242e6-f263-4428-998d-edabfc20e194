/**
 * Trade Analysis Types
 *
 * Type definitions for the trade analysis feature
 */

import { Trade } from '@adhd-trading-dashboard/shared';

// Trade Types (re-export from shared for convenience)
export type TradeDirection = 'long' | 'short';
export type TradeStatus = 'win' | 'loss' | 'breakeven';
export type TradeTimeframe = '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | 'daily';
export type TradingSession = 'pre-market' | 'regular' | 'after-hours';

// Re-export Trade interface from shared package
export { Trade };

// Performance Metrics
export interface PerformanceMetrics {
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  breakeven: number;
  winRate: number;
  averageWin: number;
  averageLoss: number;
  profitFactor: number;
  totalProfitLoss: number;
  largestWin: number;
  largestLoss: number;
  averageDuration: number; // in minutes
  expectancy: number;
  maxDrawdown?: number;
  sharpeRatio?: number;
  successStreak?: number;
  netProfit?: number;
}

/**
 * Equity Point
 */
export interface EquityPoint {
  /** The date of the equity point */
  date: string;
  /** The equity value */
  equity: number;
  /** The baseline value for comparison */
  baseline: number;
}

/**
 * Distribution Bar
 */
export interface DistributionBar {
  /** The range label */
  range: string;
  /** The count of trades in the range */
  count: number;
  /** Whether the range represents winning trades */
  isWin: boolean;
}

/**
 * Trade Summary
 */
export interface TradeSummary {
  /** The total number of trades */
  totalTrades: number;
  /** The number of winning trades */
  winningTrades: number;
  /** The number of losing trades */
  losingTrades: number;
  /** The number of break-even trades */
  breakEvenTrades: number;
  /** The total profit */
  totalProfit: number;
  /** The total fees */
  totalFees: number;
  /** The net profit */
  netProfit: number;
  /** The win rate (0-1) */
  winRate: number;
  /** The average win amount */
  averageWin: number;
  /** The average loss amount */
  averageLoss: number;
  /** The profit factor (average win / average loss) */
  profitFactor: number;
}

// Performance by Category
export interface CategoryPerformance {
  category: string;
  value: string;
  trades: number;
  winRate: number;
  profitLoss: number;
  averageProfitLoss: number;
}

// Performance by Time
export interface TimePerformance {
  timeSlot: string;
  trades: number;
  winRate: number;
  profitLoss: number;
}

// Equity Curve Data Point
export interface EquityPoint {
  date: string;
  balance: number;
  tradeNumber: number;
  profitLoss: number;
}

// Distribution Chart Data
export interface DistributionBar {
  range: string;
  count: number;
  percentage: number;
  totalPnL: number;
}

// Filter Options
export interface TradeFilters {
  dateRange: {
    startDate: string; // ISO date string
    endDate: string; // ISO date string
  };
  symbols?: string[];
  directions?: TradeDirection[];
  statuses?: TradeStatus[];
  timeframes?: TradeTimeframe[];
  sessions?: TradingSession[];
  strategies?: string[];
  tags?: string[];
  minProfitLoss?: number;
  maxProfitLoss?: number;
}

// User Preferences
export interface UserPreferences {
  defaultDateRange: 'today' | 'yesterday' | 'week' | 'month' | 'quarter' | 'year' | 'custom';
  defaultView: 'summary' | 'trades' | 'charts';
  chartTypes: {
    performance: 'bar' | 'line';
    distribution: 'pie' | 'bar';
    timeAnalysis: 'heatmap' | 'bar';
  };
  tableColumns: string[];
  favoriteStrategies: string[];
  favoriteTags: string[];
}

// Trade Analysis Data
export interface TradeAnalysisData {
  trades: Trade[];
  metrics: PerformanceMetrics;
  symbolPerformance: CategoryPerformance[];
  strategyPerformance: CategoryPerformance[];
  timeframePerformance: CategoryPerformance[];
  sessionPerformance: CategoryPerformance[];
  setupPerformance?: CategoryPerformance[];
  directionPerformance?: CategoryPerformance[];
  timeOfDayPerformance: TimePerformance[];
  dayOfWeekPerformance: TimePerformance[];
  monthlyPerformance?: TimePerformance[];
  equityCurve?: EquityPoint[];
  distributionData?: DistributionBar[];
  totalTrades?: number;
  dateRange?: {
    start: number;
    end: number;
  };
  lastUpdated?: string;
}

// Trade Analysis State
export interface TradeAnalysisState {
  data: TradeAnalysisData | null;
  filters: TradeFilters;
  preferences: UserPreferences;
  isLoading: boolean;
  error: string | null;
  selectedTradeId: string | null;
}

// Trade Analysis Actions
export type TradeAnalysisAction =
  | { type: 'FETCH_DATA_START' }
  | { type: 'FETCH_DATA_SUCCESS'; payload: TradeAnalysisData }
  | { type: 'FETCH_DATA_ERROR'; payload: string }
  | { type: 'UPDATE_FILTERS'; payload: Partial<TradeFilters> }
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<UserPreferences> }
  | { type: 'SELECT_TRADE'; payload: string | null }
  | { type: 'RESET_FILTERS' };
