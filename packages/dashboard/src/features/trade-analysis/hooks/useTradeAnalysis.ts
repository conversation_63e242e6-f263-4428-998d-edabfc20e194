/**
 * useTradeAnalysis Hook
 *
 * Custom hook for managing trade analysis data and state
 */

import { useState, useEffect, useCallback } from 'react';
import { tradeStorageService, Trade } from '@adhd-trading-dashboard/shared';
import {
  useTradeAnalysisStore,
  useTradeAnalysisSelector,
  useTradeAnalysisActions,
  selectFilter,
  selectSort,
  selectPage,
  selectPageSize,
  selectPaginatedTrades,
  selectTotalPages,
  selectTradeSummary,
  selectIsLoading,
  selectError,
  tradeAnalysisActions,
} from './tradeAnalysisState';
import { PerformanceMetrics, EquityPoint, DistributionBar, TradeFilters } from '../types';
import {
  fetchTradeAnalysisData,
  fetchFilterOptions,
  fetchTradeStatistics,
} from '../services/tradeAnalysisApi';

/**
 * useTradeAnalysis Hook
 *
 * Fetches and provides data for trade analysis charts and metrics
 */
export const useTradeAnalysis = () => {
  const { state, dispatch } = useTradeAnalysisStore();
  const actions = useTradeAnalysisActions(tradeAnalysisActions);

  // Selectors
  const filter = useTradeAnalysisSelector(selectFilter);
  const sort = useTradeAnalysisSelector(selectSort);
  const page = useTradeAnalysisSelector(selectPage);
  const pageSize = useTradeAnalysisSelector(selectPageSize);
  const paginatedTrades = useTradeAnalysisSelector(selectPaginatedTrades);
  const totalPages = useTradeAnalysisSelector(selectTotalPages);
  const tradeSummary = useTradeAnalysisSelector(selectTradeSummary);
  const isLoading = useTradeAnalysisSelector(selectIsLoading);
  const error = useTradeAnalysisSelector(selectError);

  // Direct service state
  const [tradingData, setTradingData] = useState<any>(null);
  const [isApiLoading, setIsApiLoading] = useState<boolean>(false);
  const [apiError, setApiError] = useState<Error | null>(null);

  // Options for filtering trades
  const tradingDataOptions = {
    startDate: filter.startDate,
    endDate: filter.endDate,
    symbol: filter.symbol,
    action: filter.action,
    minProfit: filter.minProfit,
    maxProfit: filter.maxProfit,
    sortBy: sort.field,
    sortDirection: sort.direction,
  };

  // Derived state for charts
  const [equityCurveData, setEquityCurveData] = useState<EquityPoint[]>([]);
  const [distributionData, setDistributionData] = useState<DistributionBar[]>([]);
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);

  // Fetch trades when component mounts or filter changes
  useEffect(() => {
    fetchTrades();
  }, [fetchTrades]);

  // Update trades when API data changes
  useEffect(() => {
    if (tradingData?.data) {
      const trades: Trade[] = tradingData.data.map((item) => ({
        id: `${item.date}-${item.symbol}-${item.action}`,
        date: item.date,
        symbol: item.symbol,
        action: item.action as 'BUY' | 'SELL',
        price: item.price,
        quantity: item.quantity,
        profit: item.profit,
        fees: 0, // Add fees if available in the API
        notes: '', // Add notes if available in the API
        tags: [], // Add tags if available in the API
      }));
      actions.setTrades(trades);
    }
  }, [tradingData, actions]);

  // Update loading state
  useEffect(() => {
    actions.setLoading(isApiLoading);
  }, [isApiLoading, actions]);

  // Update error state
  useEffect(() => {
    if (apiError) {
      actions.setError(apiError.message);
    } else {
      actions.setError(null);
    }
  }, [apiError, actions]);

  // Generate chart data from trades
  useEffect(() => {
    if (state.trades.length > 0) {
      // Generate equity curve data
      const sortedTrades = [...state.trades].sort(
        (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
      );

      let runningEquity = 10000; // Starting equity
      const equityData: EquityPoint[] = [];

      sortedTrades.forEach((trade, index) => {
        runningEquity += trade.profit;
        equityData.push({
          date: trade.date,
          equity: runningEquity,
          baseline: 10000 + index * 100, // Simple baseline for comparison
        });
      });

      setEquityCurveData(equityData);

      // Generate distribution data
      const profitRanges = [
        { min: -Infinity, max: -500, label: '-$500+', isWin: false },
        { min: -499, max: -300, label: '-$499 to -$300', isWin: false },
        { min: -299, max: -100, label: '-$299 to -$100', isWin: false },
        { min: -99, max: 0, label: '-$99 to $0', isWin: false },
        { min: 1, max: 100, label: '$1 to $100', isWin: true },
        { min: 101, max: 300, label: '$101 to $300', isWin: true },
        { min: 301, max: 500, label: '$301 to $500', isWin: true },
        { min: 501, max: Infinity, label: '$501+', isWin: true },
      ];

      const distribution = profitRanges.map((range) => {
        const count = state.trades.filter(
          (trade) => trade.profit >= range.min && trade.profit <= range.max
        ).length;

        return {
          range: range.label,
          count,
          isWin: range.isWin,
        };
      });

      setDistributionData(distribution);

      // Generate metrics
      if (tradeSummary) {
        setMetrics({
          winRate: tradeSummary.winRate,
          profitFactor: tradeSummary.profitFactor,
          averageWin: tradeSummary.averageWin,
          averageLoss: tradeSummary.averageLoss,
          totalTrades: tradeSummary.totalTrades,
          netProfit: tradeSummary.netProfit,
          expectancy:
            tradeSummary.averageWin * tradeSummary.winRate +
            tradeSummary.averageLoss * (1 - tradeSummary.winRate),
          maxDrawdown: 0.12, // Calculate from equity curve
          sharpeRatio: 1.8, // Calculate from returns
          successStreak: 7, // Calculate from trades
        });
      }
    }
  }, [state.trades, tradeSummary]);

  // Fetch trades using the real trade analysis API
  const fetchTrades = useCallback(async () => {
    try {
      setIsApiLoading(true);
      actions.setLoading(true);

      // Convert current filter to TradeFilters format
      const analysisFilters: TradeFilters = {
        dateRange: {
          startDate:
            filter.startDate ||
            new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          endDate: filter.endDate || new Date().toISOString().split('T')[0],
        },
        symbols: filter.symbol ? [filter.symbol] : undefined,
        directions: filter.direction
          ? [filter.direction.toLowerCase() as 'long' | 'short']
          : undefined,
        sessions: filter.session ? [filter.session] : undefined,
        strategies: filter.modelType ? [filter.modelType] : undefined,
        minProfitLoss: filter.minProfit,
        maxProfitLoss: filter.maxProfit,
      };

      // Fetch real trade analysis data
      const analysisData = await fetchTradeAnalysisData(analysisFilters);

      // Update state with the fetched data
      actions.setTrades(
        analysisData.trades.map((trade) => ({
          trade: {
            id: parseInt(trade.id) || 0,
            date: trade.entryTime.split('T')[0],
            model_type: trade.strategy,
            session: trade.session,
            setup: trade.setup,
            direction: trade.direction === 'long' ? 'Long' : 'Short',
            market: trade.symbol,
            r_multiple: trade.rMultiple,
            entry_price: trade.entryPrice,
            exit_price: trade.exitPrice,
            achieved_pl: trade.profitLoss,
            win_loss: trade.status === 'win' ? 'Win' : 'Loss',
            pattern_quality_rating: trade.patternQuality,
            notes: trade.notes,
          },
        }))
      );

      setApiError(null);
    } catch (error) {
      console.error('Error fetching trade analysis data:', error);
      setApiError(error instanceof Error ? error : new Error('Unknown error fetching trades'));
      actions.setError(error instanceof Error ? error.message : 'Unknown error');
    } finally {
      setIsApiLoading(false);
      actions.setLoading(false);
    }
  }, [filter, actions]);

  // Export trades
  const exportTrades = useCallback(() => {
    const trades = state.trades;
    const csv = [
      // CSV header
      ['Date', 'Symbol', 'Action', 'Price', 'Quantity', 'Profit', 'Fees', 'Notes', 'Tags'].join(
        ','
      ),
      // CSV rows
      ...trades.map((trade) =>
        [
          trade.date,
          trade.symbol,
          trade.action,
          trade.price,
          trade.quantity,
          trade.profit,
          trade.fees,
          `"${trade.notes || ''}"`,
          `"${trade.tags?.join(', ') || ''}"`,
        ].join(',')
      ),
    ].join('\n');

    // Create a blob and download
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `trades-export-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [state.trades]);

  return {
    // Legacy data
    metrics,
    equityCurveData,
    distributionData,

    // New state
    trades: paginatedTrades,
    filter,
    sort,
    page,
    pageSize,
    totalPages,
    tradeSummary,
    isLoading,
    error,

    // Actions
    setFilter: actions.setFilter,
    clearFilters: actions.clearFilters,
    setSort: actions.setSort,
    setPage: actions.setPage,
    setPageSize: actions.setPageSize,

    // API actions
    fetchTrades,
    exportTrades,
  };
};
