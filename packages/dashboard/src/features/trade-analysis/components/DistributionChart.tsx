/**
 * DistributionChart Component
 *
 * Displays a bar chart showing the distribution of trade outcomes.
 */

import React from 'react';
import styled from 'styled-components';
// Removed unused imports - will be added back when needed for real data integration

// Local interface for chart data
export interface DistributionBar {
  /** The range label */
  range: string;
  /** The count of trades in the range */
  count: number;
  /** Whether the range represents winning trades */
  isWin: boolean;
}

interface DistributionChartProps {
  data: DistributionBar[];
  isLoading: boolean;
}

const ChartContainer = styled.div`
  width: 100%;
  height: 300px;
  position: relative;
  margin-top: 20px;
  margin-bottom: 40px;
`;

const ChartSvg = styled.svg`
  width: 100%;
  height: 100%;
  overflow: visible;
`;

const Bar = styled.rect<{ isWin: boolean }>`
  fill: ${({ theme, isWin }) => (isWin ? theme.colors.success : theme.colors.error)};
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.8;
  }
`;

const XAxis = styled.line`
  stroke: ${({ theme }) => theme.colors.border};
  stroke-width: 1;
`;

const YAxis = styled.line`
  stroke: ${({ theme }) => theme.colors.border};
  stroke-width: 1;
`;

const ChartLabel = styled.text`
  font-size: 12px;
  fill: ${({ theme }) => theme.colors.textSecondary};
`;

const LoadingPlaceholder = styled.div`
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${({ theme }) => theme.colors.textSecondary};
  background-color: ${({ theme }) => theme.colors.chartGrid};
`;

/**
 * DistributionChart Component
 *
 * Visualizes the distribution of winning and losing trades by range
 */
const DistributionChart: React.FC<DistributionChartProps> = ({ data, isLoading }) => {
  if (isLoading) {
    return <LoadingPlaceholder>Loading distribution data...</LoadingPlaceholder>;
  }

  if (!data || data.length === 0) {
    return <LoadingPlaceholder>No distribution data available</LoadingPlaceholder>;
  }

  // Chart dimensions and margins
  const margin = { top: 20, right: 30, bottom: 60, left: 50 };
  const width = 800 - margin.left - margin.right;
  const height = 300 - margin.top - margin.bottom;

  // Find max count for y-scale
  const maxCount = Math.max(...data.map((d) => d.count));

  // Calculate bar width based on number of bars
  const barWidth = (width / data.length) * 0.8;
  const barSpacing = (width / data.length) * 0.2;

  return (
    <ChartContainer>
      <ChartSvg
        viewBox={`0 0 ${width + margin.left + margin.right} ${height + margin.top + margin.bottom}`}
      >
        <g transform={`translate(${margin.left}, ${margin.top})`}>
          {/* Y-axis */}
          <YAxis x1={0} y1={0} x2={0} y2={height} />

          {/* Y-axis labels */}
          {Array.from({ length: 6 }, (_, i) => {
            const value = Math.ceil(maxCount / 5) * i;
            const y = height - (value / maxCount) * height;
            return (
              <g key={`y-label-${i}`} transform={`translate(-10, ${y})`}>
                <ChartLabel textAnchor="end" dominantBaseline="middle">
                  {value}
                </ChartLabel>
                <line x1={0} y1={0} x2={width} y2={0} stroke="#eee" strokeDasharray="3,3" />
              </g>
            );
          })}

          {/* X-axis */}
          <XAxis x1={0} y1={height} x2={width} y2={height} />

          {/* Bars */}
          {data.map((d, i) => {
            const barHeight = (d.count / maxCount) * height;
            const x = i * (barWidth + barSpacing) + barSpacing / 2;
            const y = height - barHeight;

            return (
              <g key={`bar-${i}`}>
                <Bar x={x} y={y} width={barWidth} height={barHeight} isWin={d.isWin} rx={2} />

                {/* X-axis label */}
                <g transform={`translate(${x + barWidth / 2}, ${height + 20})`}>
                  <ChartLabel textAnchor="middle" transform="rotate(45)" x={0} y={0}>
                    {d.range}
                  </ChartLabel>
                </g>
              </g>
            );
          })}

          {/* Chart title */}
          <ChartLabel x={width / 2} y={-5} textAnchor="middle" style={{ fontSize: '14px' }}>
            Distribution of P&L by Range
          </ChartLabel>
        </g>
      </ChartSvg>
    </ChartContainer>
  );
};

export default DistributionChart;
