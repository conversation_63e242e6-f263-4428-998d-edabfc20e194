/**
 * Market Overview Component
 *
 * Main container component for displaying market overview information.
 */
import React from 'react';
import { Card } from '@adhd-trading-dashboard/shared';
import { MarketOverview as MarketOverviewType } from '../types';
import styled from 'styled-components';
import { MarketSummary } from './MarketSummary';
import { MarketIndicators } from './MarketIndicators';
import { MarketNews } from './MarketNews';

export interface MarketOverviewProps {
  /** The market overview data */
  marketOverview: MarketOverviewType | null;
  /** Whether the component is in a loading state */
  isLoading?: boolean;
  /** The error message, if any */
  error?: string | null;
  /** Function called when the refresh button is clicked */
  onRefresh?: () => void;
  /** Additional class name */
  className?: string;
}

// Styled components
const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing.md};
`;

/**
 * Market Overview Component
 *
 * Main container component for displaying market overview information.
 */
export const MarketOverview: React.FC<MarketOverviewProps> = ({
  marketOverview,
  isLoading = false,
  error = null,
  onRefresh,
  className,
}) => {
  // Loading state
  if (isLoading) {
    return (
      <Card title="Market Overview">
        <div style={{ padding: '24px', textAlign: 'center' }}>Loading market data...</div>
      </Card>
    );
  }

  // Error state
  if (error) {
    return (
      <Card title="Market Overview">
        <div style={{ padding: '24px', textAlign: 'center', color: '#f44336' }}>
          Error: {error}
          {onRefresh && (
            <button
              onClick={onRefresh}
              style={{
                marginLeft: '16px',
                padding: '8px 16px',
                background: '#f0f0f0',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Retry
            </button>
          )}
        </div>
      </Card>
    );
  }

  // Empty state
  if (!marketOverview) {
    return (
      <Card title="Market Overview">
        <div style={{ padding: '24px', textAlign: 'center' }}>
          No market data available.
          {onRefresh && (
            <button
              onClick={onRefresh}
              style={{
                marginLeft: '16px',
                padding: '8px 16px',
                background: '#f0f0f0',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              Refresh
            </button>
          )}
        </div>
      </Card>
    );
  }

  return (
    <Card
      title="Market Overview"
      actions={
        onRefresh ? (
          <button
            onClick={onRefresh}
            style={{ background: 'none', border: 'none', cursor: 'pointer', color: 'inherit' }}
          >
            🔄 Refresh
          </button>
        ) : undefined
      }
    >
      <Container className={className}>
        <MarketSummary
          sentiment={marketOverview.sentiment}
          summary={marketOverview.summary}
          lastUpdated={marketOverview.lastUpdated}
        />

        <MarketIndicators indices={marketOverview.indices} />

        {marketOverview.economicEvents && marketOverview.economicEvents.length > 0 && (
          <MarketNews events={marketOverview.economicEvents} />
        )}
      </Container>
    </Card>
  );
};
