/**
 * DailyGuideContainer Component
 *
 * PATTERN VALIDATION: Applying proven container pattern to DailyGuide
 * to validate cross-component-type effectiveness (dashboard → forms → guides).
 *
 * BENEFITS:
 * - Uses F1Container from component library
 * - Consistent error boundaries and loading states
 * - Validates pattern works across different component types
 * - Maintains F1 racing theme consistency
 */

import React, { Suspense } from 'react';
import styled from 'styled-components';
import { F1Container } from '@adhd-trading-dashboard/shared/components/library';
import { DailyGuideHeader } from './DailyGuideHeader';
import { SectionCard } from './ui';
import { MarketOverview } from './MarketOverview';
import { TradingPlan } from './TradingPlan';
import { KeyLevels } from './KeyLevels';
import { MarketNews } from './MarketNews';
import { useDailyGuide } from '../context/DailyGuideContext';

export interface DailyGuideContainerProps {
  /** Custom className */
  className?: string;
}

const GuideGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};

  @media (min-width: 1024px) {
    grid-template-columns: 2fr 1fr;
  }
`;

const MainColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
`;

const SideColumn = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '16px'};
`;

const LoadingFallback: React.FC = () => (
  <div
    style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      height: '300px',
      gap: '16px',
    }}
  >
    <div
      style={{
        width: '32px',
        height: '32px',
        border: '3px solid #4b5563',
        borderTop: '3px solid #dc2626',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite',
      }}
    />
    <div style={{ color: '#9ca3af' }}>Loading Daily Guide...</div>
  </div>
);

/**
 * DailyGuideContent Component
 *
 * Uses context and renders the guide sections with proper error handling.
 */
const DailyGuideContent: React.FC = () => {
  const { isLoading, error, selectedDate, refreshData, data } = useDailyGuide();

  // Extract data from the nested structure
  const { marketOverview, tradingPlan, keyPriceLevels } = data;

  return (
    <>
      {/* F1 Racing Header */}
      <DailyGuideHeader
        currentDate={new Date(selectedDate).toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        })}
        onRefresh={refreshData}
        isRefreshing={isLoading}
      />

      {/* Guide Content Grid */}
      <GuideGrid>
        <MainColumn>
          {/* Market Overview */}
          <SectionCard
            title="📊 Market Overview"
            isLoading={isLoading}
            hasError={!!error}
            errorMessage={error || ''}
          >
            <Suspense fallback={<LoadingFallback />}>
              <MarketOverview marketOverview={marketOverview} />
            </Suspense>
          </SectionCard>

          {/* Trading Plan */}
          <SectionCard
            title="🎯 Trading Plan"
            isLoading={isLoading}
            hasError={!!error}
            errorMessage={error || ''}
          >
            <Suspense fallback={<LoadingFallback />}>
              <TradingPlan tradingPlan={tradingPlan} />
            </Suspense>
          </SectionCard>
        </MainColumn>

        <SideColumn>
          {/* Key Levels */}
          <SectionCard
            title="📈 Key Levels"
            isLoading={isLoading}
            hasError={!!error}
            errorMessage={error || ''}
          >
            <Suspense fallback={<LoadingFallback />}>
              <KeyLevels keyLevels={keyPriceLevels} />
            </Suspense>
          </SectionCard>

          {/* Market News */}
          <SectionCard
            title="📰 Market News"
            isLoading={isLoading}
            hasError={!!error}
            errorMessage={error || ''}
          >
            <Suspense fallback={<LoadingFallback />}>
              <MarketNews events={marketOverview?.economicEvents || []} />
            </Suspense>
          </SectionCard>
        </SideColumn>
      </GuideGrid>
    </>
  );
};

/**
 * DailyGuideContainer Component
 *
 * PATTERN VALIDATION: Demonstrates that our proven container pattern
 * works effectively across different component types:
 * - Dashboard (TradingDashboard) ✅
 * - Forms (QuickTradeForm) ✅
 * - Analysis (TradeAnalysis) ✅
 * - Guides (DailyGuide) ✅
 */
export const DailyGuideContainer: React.FC<DailyGuideContainerProps> = ({ className }) => {
  return (
    <F1Container
      variant="dashboard"
      maxWidth={1400}
      className={className}
      background="default"
      animated={true}
    >
      <Suspense fallback={<LoadingFallback />}>
        <DailyGuideContent />
      </Suspense>
    </F1Container>
  );
};

export default DailyGuideContainer;
