/**
 * F1FormField Component
 *
 * Standardized form field component that integrates with useFormField hook
 * and provides consistent F1-themed styling.
 *
 * PROVEN PATTERN: Extracted from QuickTradeForm and TradingPlan form patterns
 *
 * FEATURES:
 * - Integration with useFormField hook
 * - F1 racing theme consistency
 * - Built-in validation display
 * - Multiple input types support
 * - Accessibility compliant
 */

import React, { ReactNode } from 'react';
import styled, { css } from 'styled-components';
import { UseFormFieldReturn } from '../../../hooks/useFormField';
import { spacing, fontSizes, borderRadius } from '../../../theme/tokens';

export interface F1FormFieldProps {
  /** Field label */
  label: string;
  /** Form field hook return */
  field: UseFormFieldReturn;
  /** Input type */
  type?: 'text' | 'number' | 'email' | 'password' | 'tel' | 'url' | 'select' | 'textarea';
  /** Placeholder text */
  placeholder?: string;
  /** Whether field is required */
  required?: boolean;
  /** Whether field is disabled */
  disabled?: boolean;
  /** Help text */
  helpText?: string;
  /** Select options (for select type) */
  options?: Array<{ value: string | number; label: string }>;
  /** Custom input props */
  inputProps?: Record<string, any>;
  /** Custom className */
  className?: string;
  /** Field size */
  size?: 'sm' | 'md' | 'lg';
  /** Field variant */
  variant?: 'default' | 'trading' | 'analysis';
  /** Icon or prefix content */
  prefix?: ReactNode;
  /** Suffix content */
  suffix?: ReactNode;
}

const FieldContainer = styled.div<{ $size: F1FormFieldProps['size'] }>`
  display: flex;
  flex-direction: column;
  gap: ${({ $size }) => {
    const sizeMap = {
      sm: spacing.xs,
      md: spacing.sm,
      lg: spacing.md,
    };
    return sizeMap[$size || 'md'];
  }};
`;

const Label = styled.label<{ $required: boolean; $variant: F1FormFieldProps['variant'] }>`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};

  /* Variant-specific styling */
  ${({ $variant }) => {
    switch ($variant) {
      case 'trading':
        return css`
          text-transform: uppercase;
          letter-spacing: 0.025em;
        `;
      case 'analysis':
        return css`
          font-weight: 500;
        `;
      default:
        return css``;
    }
  }}

  /* Required indicator */
  ${({ $required, theme }) =>
    $required &&
    css`
      &::after {
        content: '*';
        color: ${theme.colors?.primary || '#dc2626'};
        margin-left: 2px;
      }
    `}
`;

const InputContainer = styled.div<{ $hasError: boolean; $disabled: boolean }>`
  position: relative;
  display: flex;
  align-items: center;

  ${({ $disabled }) =>
    $disabled &&
    css`
      opacity: 0.6;
      pointer-events: none;
    `}
`;

const baseInputStyles = css<{ $hasError: boolean; $size: F1FormFieldProps['size'] }>`
  width: 100%;
  border: 1px solid
    ${({ $hasError, theme }) => ($hasError ? theme.colors?.error || '#f44336' : '#4b5563')};
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  background: ${({ theme }) => theme.colors?.background || '#111827'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-family: inherit;
  transition: all 0.2s ease;

  /* Size-based styling */
  ${({ $size }) => {
    const sizeMap = {
      sm: css`
        padding: ${spacing.xs} ${spacing.sm};
        font-size: ${fontSizes.sm};
      `,
      md: css`
        padding: ${spacing.sm} ${spacing.md};
        font-size: ${fontSizes.md};
      `,
      lg: css`
        padding: ${spacing.md} ${spacing.lg};
        font-size: ${fontSizes.lg};
      `,
    };
    return sizeMap[$size || 'md'];
  }}

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || '#dc2626'};
    box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
  }

  &:disabled {
    background: #374151;
    color: #9ca3af;
    cursor: not-allowed;
  }

  &::placeholder {
    color: #6b7280;
  }
`;

const Input = styled.input<{ $hasError: boolean; $size: F1FormFieldProps['size'] }>`
  ${baseInputStyles}
`;

const Select = styled.select<{ $hasError: boolean; $size: F1FormFieldProps['size'] }>`
  ${baseInputStyles}
  cursor: pointer;

  option {
    background: ${({ theme }) => theme.colors?.background || '#111827'};
    color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  }
`;

const TextArea = styled.textarea<{ $hasError: boolean; $size: F1FormFieldProps['size'] }>`
  ${baseInputStyles}
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
`;

const PrefixContainer = styled.div`
  position: absolute;
  left: 12px;
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
  pointer-events: none;
  z-index: 1;
`;

const SuffixContainer = styled.div`
  position: absolute;
  right: 12px;
  display: flex;
  align-items: center;
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
`;

const ErrorMessage = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.error || '#f44336'};
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;

const HelpText = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || '#9ca3af'};
`;

const ValidationIndicator = styled.div<{ $valid: boolean; $validating: boolean }>`
  position: absolute;
  right: 8px;
  display: flex;
  align-items: center;

  ${({ $validating }) =>
    $validating &&
    css`
      &::after {
        content: '';
        width: 12px;
        height: 12px;
        border: 2px solid #4b5563;
        border-top: 2px solid #dc2626;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    `}

  ${({ $valid, $validating }) =>
    !$validating &&
    css`
      color: ${$valid ? '#22c55e' : '#f44336'};
      &::after {
        content: '${$valid ? '✓' : '✗'}';
      }
    `}
`;

/**
 * F1FormField Component
 *
 * Standardized form field that integrates with useFormField hook
 * and provides consistent F1-themed styling.
 *
 * @example
 * ```typescript
 * const symbolField = useFormField({
 *   initialValue: 'MNQ',
 *   required: true,
 *   validationRules: [validationRules.required()],
 * });
 *
 * <F1FormField
 *   label="Symbol"
 *   field={symbolField}
 *   type="text"
 *   placeholder="Enter symbol"
 *   variant="trading"
 *   required={true}
 * />
 * ```
 */
export const F1FormField: React.FC<F1FormFieldProps> = (props) => {
  const {
    label,
    field,
    type = 'text',
    placeholder,
    required = false,
    disabled = false,
    helpText,
    options = [],
    inputProps = {},
    className,
    size = 'md',
    variant = 'default',
    prefix,
    suffix,
  } = props;
  const hasError = !!(field.error && field.touched);
  const showValidation = field.touched && !field.validating;

  const renderInput = () => {
    const commonProps = {
      id: (inputProps as any).id || label.toLowerCase().replace(/\s+/g, '-'),
      value: field.value,
      onChange: field.setValue,
      onBlur: () => field.setTouched(true),
      disabled,
      placeholder,
      $hasError: hasError,
      $size: size as NonNullable<F1FormFieldProps['size']>,
      ...inputProps,
    };

    switch (type) {
      case 'select':
        return (
          <Select {...commonProps}>
            {placeholder && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Select>
        );

      case 'textarea':
        return <TextArea {...commonProps} />;

      default:
        return <Input {...commonProps} type={type} />;
    }
  };

  return (
    <FieldContainer $size={size as NonNullable<F1FormFieldProps['size']>} className={className}>
      <Label
        $required={required}
        $variant={variant as NonNullable<F1FormFieldProps['variant']>}
        htmlFor={(inputProps as any).id || label.toLowerCase().replace(/\s+/g, '-')}
      >
        {label}
      </Label>

      <InputContainer $hasError={hasError} $disabled={disabled}>
        {prefix && <PrefixContainer>{prefix}</PrefixContainer>}

        {renderInput()}

        {suffix && <SuffixContainer>{suffix}</SuffixContainer>}

        {showValidation && (
          <ValidationIndicator $valid={field.valid} $validating={field.validating} />
        )}
      </InputContainer>

      {hasError && <ErrorMessage>⚠️ {field.error}</ErrorMessage>}

      {helpText && !hasError && <HelpText>{helpText}</HelpText>}
    </FieldContainer>
  );
};

export default F1FormField;
