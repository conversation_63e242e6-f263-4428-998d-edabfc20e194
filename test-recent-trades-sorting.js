/**
 * Test script to verify Recent Trades sorting
 * 
 * This script adds test trades to IndexedDB and verifies that the Recent Trades
 * section shows them in the correct order (newest first).
 */

// Test data - trades with different dates
const testTrades = [
  {
    trade: {
      id: 1,
      date: '2024-01-15', // Oldest
      market: 'MNQ',
      direction: 'Long',
      session: 'NY Open',
      model_type: 'RD-Cont',
      entry_price: 15000,
      exit_price: 15100,
      no_of_contracts: 1,
      achieved_pl: 100,
      r_multiple: 1.0,
      win_loss: 'Win',
      pattern_quality_rating: 4,
      entry_time: '09:30',
      exit_time: '10:15',
      notes: 'Test trade 1 - oldest',
    },
    fvg_details: null,
    setup: null,
    analysis: null,
  },
  {
    trade: {
      id: 2,
      date: '2024-01-16',
      market: 'NQ',
      direction: 'Short',
      session: 'Lunch Macro',
      model_type: 'FVG-RD',
      entry_price: 15200,
      exit_price: 15050,
      no_of_contracts: 2,
      achieved_pl: 300,
      r_multiple: 1.5,
      win_loss: 'Win',
      pattern_quality_rating: 5,
      entry_time: '11:30',
      exit_time: '12:15',
      notes: 'Test trade 2',
    },
    fvg_details: null,
    setup: null,
    analysis: null,
  },
  {
    trade: {
      id: 3,
      date: '2024-01-17',
      market: 'ES',
      direction: 'Long',
      session: 'MOC',
      model_type: 'True-RD',
      entry_price: 4800,
      exit_price: 4750,
      no_of_contracts: 1,
      achieved_pl: -50,
      r_multiple: -0.5,
      win_loss: 'Loss',
      pattern_quality_rating: 2,
      entry_time: '15:30',
      exit_time: '16:00',
      notes: 'Test trade 3',
    },
    fvg_details: null,
    setup: null,
    analysis: null,
  },
  {
    trade: {
      id: 4,
      date: '2024-01-18',
      market: 'MES',
      direction: 'Short',
      session: 'NY Open',
      model_type: 'RD-Cont',
      entry_price: 4850,
      exit_price: 4900,
      no_of_contracts: 3,
      achieved_pl: -150,
      r_multiple: -1.0,
      win_loss: 'Loss',
      pattern_quality_rating: 3,
      entry_time: '09:45',
      exit_time: '10:30',
      notes: 'Test trade 4',
    },
    fvg_details: null,
    setup: null,
    analysis: null,
  },
  {
    trade: {
      id: 5,
      date: '2024-01-19', // Newest
      market: 'NQ',
      direction: 'Long',
      session: 'Lunch Macro',
      model_type: 'FVG-RD',
      entry_price: 15300,
      exit_price: 15450,
      no_of_contracts: 2,
      achieved_pl: 300,
      r_multiple: 2.0,
      win_loss: 'Win',
      pattern_quality_rating: 5,
      entry_time: '12:00',
      exit_time: '12:45',
      notes: 'Test trade 5 - newest',
    },
    fvg_details: null,
    setup: null,
    analysis: null,
  },
];

console.log('Test trades created with dates:');
testTrades.forEach(trade => {
  console.log(`Trade ${trade.trade.id}: ${trade.trade.date} (${trade.trade.notes})`);
});

console.log('\nExpected order in Recent Trades (newest first):');
console.log('1. Trade 5: 2024-01-19 (newest)');
console.log('2. Trade 4: 2024-01-18');
console.log('3. Trade 3: 2024-01-17');
console.log('4. Trade 2: 2024-01-16');
console.log('5. Trade 1: 2024-01-15 (oldest)');

console.log('\nTo test:');
console.log('1. Open browser console');
console.log('2. Copy and paste this script');
console.log('3. Check the Recent Trades section on the dashboard');
console.log('4. Verify trades are shown in newest-first order');

// Export for potential use in browser console
if (typeof window !== 'undefined') {
  window.testTrades = testTrades;
  console.log('Test trades available as window.testTrades');
}
