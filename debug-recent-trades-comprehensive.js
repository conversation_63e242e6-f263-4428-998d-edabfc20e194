/**
 * Comprehensive Recent Trades Debugging Script
 * 
 * This script will help identify:
 * 1. All Recent Trades components in the DOM
 * 2. Data flow from IndexedDB to UI
 * 3. Multiple data sources or transformations
 * 4. Component hierarchy and routing
 */

console.log('🔍 COMPREHENSIVE RECENT TRADES DEBUG ANALYSIS');
console.log('='.repeat(60));

// Step 1: Check if our debug logs are appearing
function checkDebugLogs() {
  console.log('\n📊 Step 1: Checking for our debug logs...');
  console.log('Look for these patterns in console:');
  console.log('- 🔄 DataTransformers.convertToTradeFormat called with X trades');
  console.log('- 📅 Raw trade dates (before sorting)');
  console.log('- 📅 Sorted trade dates (newest first)');
  console.log('- 🎯 TradingDashboardContainer passing recent trades');
  
  // Force a refresh to trigger data loading
  console.log('\n🔄 Forcing data refresh...');
  if (window.location.pathname === '/') {
    window.location.reload();
  }
}

// Step 2: Find all Recent Trades components in DOM
function findRecentTradesComponents() {
  console.log('\n📋 Step 2: Finding all Recent Trades components in DOM...');
  
  const components = [];
  
  // Look for components with "Recent Trades" text
  const elementsWithText = Array.from(document.querySelectorAll('*')).filter(el => 
    el.textContent && el.textContent.includes('Recent Trades')
  );
  
  elementsWithText.forEach((el, index) => {
    console.log(`\n🎯 Found Recent Trades component #${index + 1}:`);
    console.log(`- Element:`, el.tagName);
    console.log(`- Classes:`, el.className);
    console.log(`- Text content:`, el.textContent.substring(0, 100) + '...');
    console.log(`- Parent component:`, el.closest('[class*="Container"], [class*="Panel"], [class*="Table"]'));
    
    // Look for table structure
    const table = el.closest('table') || el.querySelector('table');
    if (table) {
      console.log(`- Has table structure: YES`);
      const rows = table.querySelectorAll('tbody tr');
      console.log(`- Number of data rows: ${rows.length}`);
      
      if (rows.length > 0) {
        console.log(`- First row data:`, Array.from(rows[0].cells).map(cell => cell.textContent.trim()));
        console.log(`- Last row data:`, Array.from(rows[rows.length - 1].cells).map(cell => cell.textContent.trim()));
      }
    }
    
    components.push({
      element: el,
      hasTable: !!table,
      rowCount: table ? table.querySelectorAll('tbody tr').length : 0
    });
  });
  
  return components;
}

// Step 3: Analyze current route and active components
function analyzeCurrentRoute() {
  console.log('\n🗺️ Step 3: Analyzing current route and active components...');
  
  console.log(`- Current URL: ${window.location.href}`);
  console.log(`- Current pathname: ${window.location.pathname}`);
  console.log(`- Current hash: ${window.location.hash}`);
  
  // Check for active tabs
  const activeTabs = document.querySelectorAll('[class*="active"], [aria-selected="true"]');
  console.log(`- Active tabs/elements: ${activeTabs.length}`);
  activeTabs.forEach((tab, index) => {
    console.log(`  ${index + 1}. ${tab.textContent.trim()} (${tab.tagName})`);
  });
  
  // Check for React components in DOM
  const reactElements = document.querySelectorAll('[data-reactroot], [class*="Dashboard"], [class*="Container"]');
  console.log(`- React components found: ${reactElements.length}`);
  reactElements.forEach((el, index) => {
    if (el.className.includes('Dashboard') || el.className.includes('Container')) {
      console.log(`  ${index + 1}. ${el.className}`);
    }
  });
}

// Step 4: Check IndexedDB data directly
async function checkIndexedDBData() {
  console.log('\n💾 Step 4: Checking IndexedDB data directly...');
  
  try {
    const request = indexedDB.open('adhd-trading-dashboard', 1);
    
    request.onsuccess = function(event) {
      const db = event.target.result;
      const transaction = db.transaction(['trades'], 'readonly');
      const store = transaction.objectStore('trades');
      const getAllRequest = store.getAll();
      
      getAllRequest.onsuccess = function() {
        const trades = getAllRequest.result;
        console.log(`- Total trades in IndexedDB: ${trades.length}`);
        
        if (trades.length > 0) {
          // Sort by ID (insertion order)
          const byId = [...trades].sort((a, b) => (a.id || 0) - (b.id || 0));
          console.log('\n📅 Trades by ID (insertion order):');
          byId.slice(0, 5).forEach((trade, index) => {
            console.log(`  ${index + 1}. ID: ${trade.id}, Date: ${trade.date}, Market: ${trade.market}`);
          });
          
          // Sort by date descending (what we want)
          const byDateDesc = [...trades].sort((a, b) => new Date(b.date) - new Date(a.date));
          console.log('\n📅 Trades by Date (newest first - what we want):');
          byDateDesc.slice(0, 5).forEach((trade, index) => {
            console.log(`  ${index + 1}. ID: ${trade.id}, Date: ${trade.date}, Market: ${trade.market}`);
          });
          
          // Sort by date ascending (what we're seeing)
          const byDateAsc = [...trades].sort((a, b) => new Date(a.date) - new Date(b.date));
          console.log('\n📅 Trades by Date (oldest first - what we might be seeing):');
          byDateAsc.slice(0, 5).forEach((trade, index) => {
            console.log(`  ${index + 1}. ID: ${trade.id}, Date: ${trade.date}, Market: ${trade.market}`);
          });
        }
      };
    };
  } catch (error) {
    console.error('Error accessing IndexedDB:', error);
  }
}

// Step 5: Check React DevTools data (if available)
function checkReactDevTools() {
  console.log('\n⚛️ Step 5: Checking React component state...');
  
  // Try to access React DevTools data
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('- React DevTools detected');
    // This would require more complex inspection
  } else {
    console.log('- React DevTools not available');
  }
  
  // Check for React Fiber nodes
  const reactFiber = document.querySelector('[data-reactroot]');
  if (reactFiber && reactFiber._reactInternalFiber) {
    console.log('- React Fiber detected');
  }
}

// Step 6: Monitor network requests
function monitorNetworkRequests() {
  console.log('\n🌐 Step 6: Setting up network monitoring...');
  
  // Override fetch to monitor API calls
  const originalFetch = window.fetch;
  window.fetch = function(...args) {
    console.log('🌐 Fetch request:', args[0]);
    return originalFetch.apply(this, args);
  };
  
  console.log('- Network monitoring enabled');
  console.log('- Will log any fetch requests');
}

// Main execution function
async function runComprehensiveDebug() {
  console.log('🚀 Starting comprehensive Recent Trades debug analysis...');
  
  // Run all debug steps
  checkDebugLogs();
  
  setTimeout(() => {
    findRecentTradesComponents();
    analyzeCurrentRoute();
    checkIndexedDBData();
    checkReactDevTools();
    monitorNetworkRequests();
    
    console.log('\n✅ Debug analysis complete!');
    console.log('\n📋 SUMMARY:');
    console.log('1. Check console above for our debug logs (🔄, 📅, 🎯)');
    console.log('2. Review all Recent Trades components found');
    console.log('3. Compare IndexedDB data order with UI display');
    console.log('4. Look for multiple data sources or transformations');
    
    console.log('\n🔧 NEXT STEPS:');
    console.log('1. If debug logs are missing, our fix is not being executed');
    console.log('2. If multiple components found, identify which one is wrong');
    console.log('3. If data order differs, trace the transformation chain');
    
  }, 2000); // Wait 2 seconds for page to load
}

// Export functions for manual testing
window.debugRecentTrades = {
  runComprehensiveDebug,
  findRecentTradesComponents,
  analyzeCurrentRoute,
  checkIndexedDBData,
  checkReactDevTools,
  monitorNetworkRequests
};

// Auto-run the debug analysis
runComprehensiveDebug();
