declare function debugAndPopulateData(): Promise<void>;
declare const DB_NAME: "adhd-trading-dashboard";
declare const DB_VERSION: 2;
declare const STORES: string[];
declare class IndexedDBDebugger {
    db: IDBDatabase | null;
    init(): Promise<any>;
    checkDataState(): Promise<{}>;
    getStoreCount(storeName: any): Promise<any>;
    getSampleData(storeName: any, limit?: number): Promise<any>;
    addTestTrades(): Promise<boolean>;
    clearAllData(): Promise<void>;
    close(): void;
}
//# sourceMappingURL=debug-indexeddb.d.ts.map