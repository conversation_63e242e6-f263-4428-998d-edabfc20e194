<!DOCTYPE html>
<html>
<head>
    <title>Debug Runner</title>
</head>
<body>
    <h1>Debug <PERSON>ript Runner</h1>
    <p>Copy and paste this script into the browser console on http://localhost:3000:</p>
    
    <textarea id="debugScript" style="width: 100%; height: 400px; font-family: monospace;">
// COPY THIS ENTIRE SCRIPT AND PASTE INTO BROWSER CONSOLE ON http://localhost:3000

/**
 * Focused Recent Trades Sorting Debug Script
 */

console.log('🔍 FOCUSED SORTING DEBUG ANALYSIS');
console.log('='.repeat(50));

// Step 1: Check if we're on the correct page
function verifyCurrentPage() {
  console.log('\n📍 Step 1: Verifying current page...');
  console.log(`URL: ${window.location.href}`);
  console.log(`Pathname: ${window.location.pathname}`);
  
  if (window.location.pathname !== '/') {
    console.log('❌ NOT on main dashboard! Navigate to http://localhost:3000/');
    return false;
  }
  
  console.log('✅ On main dashboard - correct page');
  return true;
}

// Step 2: Find the Recent Trades component
function findRecentTradesComponent() {
  console.log('\n🎯 Step 2: Finding Recent Trades component...');
  
  // Look for the specific table structure we expect
  const tables = document.querySelectorAll('table');
  console.log(`Found ${tables.length} tables on page`);
  
  let recentTradesTable = null;
  
  tables.forEach((table, index) => {
    const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent?.trim());
    console.log(`Table ${index + 1} headers:`, headers);
    
    // Check if this matches RecentTradesTable headers
    if (headers.includes('Setup') && headers.includes('Market') && headers.includes('R-Multiple')) {
      console.log('✅ Found RecentTradesTable!');
      recentTradesTable = table;
    } else if (headers.includes('Symbol') && headers.includes('Result')) {
      console.log('⚠️ Found RecentTradesPanel (wrong component)');
    }
  });
  
  if (!recentTradesTable) {
    console.log('❌ RecentTradesTable not found!');
    return null;
  }
  
  return recentTradesTable;
}

// Step 3: Analyze the data in the table
function analyzeTableData(table) {
  console.log('\n📊 Step 3: Analyzing table data...');
  
  const rows = table.querySelectorAll('tbody tr');
  console.log(`Found ${rows.length} data rows`);
  
  if (rows.length === 0) {
    console.log('❌ No data rows found!');
    return [];
  }
  
  const trades = [];
  rows.forEach((row, index) => {
    const cells = Array.from(row.cells).map(cell => cell.textContent?.trim());
    if (cells.length > 0) {
      trades.push({
        index: index + 1,
        date: cells[0] || 'No date',
        setup: cells[1] || 'No setup',
        session: cells[2] || 'No session',
        direction: cells[3] || 'No direction',
        market: cells[4] || 'No market',
        entry: cells[5] || 'No entry',
        exit: cells[6] || 'No exit',
        rMultiple: cells[7] || 'No R-Multiple',
        pnl: cells[8] || 'No P&L'
      });
    }
  });
  
  console.log('\n📋 Current table data:');
  trades.forEach(trade => {
    console.log(`${trade.index}. Date: ${trade.date}, Market: ${trade.market}, Direction: ${trade.direction}`);
  });
  
  return trades;
}

// Step 4: Check date order
function checkDateOrder(trades) {
  console.log('\n📅 Step 4: Checking date order...');
  
  if (trades.length < 2) {
    console.log('⚠️ Not enough trades to check order');
    return;
  }
  
  const dates = trades.map(t => t.date).filter(d => d && d !== 'No date');
  console.log('Dates found:', dates);
  
  let isNewestFirst = true;
  let isOldestFirst = true;
  
  for (let i = 1; i < dates.length; i++) {
    const prevDate = new Date(dates[i - 1]);
    const currDate = new Date(dates[i]);
    
    if (prevDate < currDate) {
      isNewestFirst = false;
    }
    if (prevDate > currDate) {
      isOldestFirst = false;
    }
  }
  
  console.log('\n📊 Date order analysis:');
  if (isNewestFirst) {
    console.log('✅ Dates are in NEWEST FIRST order (correct)');
  } else if (isOldestFirst) {
    console.log('❌ Dates are in OLDEST FIRST order (incorrect)');
  } else {
    console.log('⚠️ Dates are in MIXED order');
  }
  
  return { isNewestFirst, isOldestFirst };
}

// Main execution function
async function runFocusedDebug() {
  console.log('🚀 Starting focused sorting debug...');
  
  // Step 1: Verify page
  if (!verifyCurrentPage()) {
    return;
  }
  
  // Step 2: Find component
  const table = findRecentTradesComponent();
  if (!table) {
    console.log('❌ Cannot proceed - Recent Trades table not found');
    return;
  }
  
  // Step 3: Analyze data
  const tableData = analyzeTableData(table);
  
  // Step 4: Check order
  const dateOrder = checkDateOrder(tableData);
  
  console.log('\n✅ Focused debug analysis complete!');
  
  return { table, tableData, dateOrder };
}

// Run the debug
runFocusedDebug();
    </textarea>
    
    <button onclick="copyToClipboard()">Copy Script to Clipboard</button>
    
    <script>
        function copyToClipboard() {
            const textarea = document.getElementById('debugScript');
            textarea.select();
            document.execCommand('copy');
            alert('Script copied to clipboard! Now paste it into the browser console on http://localhost:3000');
        }
    </script>
</body>
</html>
