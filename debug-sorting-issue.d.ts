declare function verifyCurrentPage(): boolean;
declare function findRecentTradesComponent(): null;
declare function analyzeTableData(table: any): any[];
declare function checkDateOrder(trades: any): {
    isNewestFirst: boolean;
    isOldestFirst: boolean;
} | undefined;
declare function checkDebugLogs(): void;
declare function checkDebugLogs(): void;
declare function checkDebugLogs(): void;
declare function checkIndexedDBOrder(): Promise<any>;
declare function generateActionPlan(): void;
declare function generateActionPlan(): void;
declare function generateActionPlan(tableData: any, dateOrder: any): void;
declare function runFocusedDebug(): Promise<void>;
//# sourceMappingURL=debug-sorting-issue.d.ts.map