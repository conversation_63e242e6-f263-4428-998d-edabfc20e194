/**
 * Debug Dashboard Data Script
 * 
 * Run this in the browser console to check:
 * 1. If IndexedDB has any trade data
 * 2. If the enhanced RecentTradesTable is loading
 * 3. Current component state
 */

console.log('🔍 Starting Dashboard Data Debug...');

// Check IndexedDB for trade data
async function checkIndexedDB() {
  console.log('📊 Checking IndexedDB for trade data...');
  
  try {
    // Open the database
    const request = indexedDB.open('adhd-trading-dashboard', 2);
    
    request.onsuccess = function(event) {
      const db = event.target.result;
      console.log('✅ IndexedDB opened successfully');
      console.log('📋 Available stores:', Array.from(db.objectStoreNames));
      
      // Check trades store
      if (db.objectStoreNames.contains('trades')) {
        const transaction = db.transaction(['trades'], 'readonly');
        const store = transaction.objectStore('trades');
        const countRequest = store.count();
        
        countRequest.onsuccess = function() {
          console.log('📊 Total trades in database:', countRequest.result);
          
          if (countRequest.result > 0) {
            // Get first few trades
            const getAllRequest = store.getAll();
            getAllRequest.onsuccess = function() {
              const trades = getAllRequest.result;
              console.log('📋 Sample trades:', trades.slice(0, 3));
            };
          } else {
            console.log('⚠️ No trades found in database');
            console.log('💡 You may need to add some test data');
          }
        };
      } else {
        console.log('❌ Trades store not found in database');
      }
    };
    
    request.onerror = function(event) {
      console.error('❌ Failed to open IndexedDB:', event.target.error);
    };
    
  } catch (error) {
    console.error('❌ IndexedDB check failed:', error);
  }
}

// Check if React components are mounted
function checkReactComponents() {
  console.log('⚛️ Checking React components...');
  
  // Look for the dashboard container
  const dashboardContainer = document.querySelector('[class*="DashboardLayout"]');
  if (dashboardContainer) {
    console.log('✅ Dashboard container found');
  } else {
    console.log('❌ Dashboard container not found');
  }
  
  // Look for the recent trades table
  const recentTradesTable = document.querySelector('[class*="TableContainer"]');
  if (recentTradesTable) {
    console.log('✅ Recent trades table container found');
    console.log('📊 Table content:', recentTradesTable.textContent.substring(0, 100) + '...');
  } else {
    console.log('❌ Recent trades table not found');
  }
  
  // Check for loading states
  const loadingElements = document.querySelectorAll('[class*="Loading"], [class*="loading"]');
  if (loadingElements.length > 0) {
    console.log('⏳ Loading elements found:', loadingElements.length);
  }
}

// Check current URL and tab state
function checkCurrentState() {
  console.log('🔍 Current state check...');
  console.log('📍 Current URL:', window.location.href);
  console.log('📍 Current pathname:', window.location.pathname);
  
  // Check for active tab
  const activeTab = document.querySelector('[class*="active"], [aria-selected="true"]');
  if (activeTab) {
    console.log('📋 Active tab:', activeTab.textContent);
  }
}

// Add test trade data if database is empty
async function addTestTradeData() {
  console.log('➕ Adding test trade data...');
  
  try {
    // Import the trade storage service (if available globally)
    if (window.tradeStorageService) {
      const testTrade = {
        trade: {
          id: Date.now(),
          symbol: 'EURUSD',
          date: new Date().toISOString().split('T')[0],
          direction: 'long',
          quantity: 1,
          entry_price: 1.0850,
          exit_price: 1.0900,
          profit: 50,
          result: 'win'
        },
        fvgDetails: null,
        setup: null,
        analysis: null
      };
      
      await window.tradeStorageService.saveTradeWithDetails(testTrade);
      console.log('✅ Test trade added successfully');
    } else {
      console.log('⚠️ tradeStorageService not available globally');
      console.log('💡 You can add test data through the trade form');
    }
  } catch (error) {
    console.error('❌ Failed to add test trade:', error);
  }
}

// Run all checks
async function runAllChecks() {
  console.log('🚀 Running comprehensive dashboard debug...');
  
  checkCurrentState();
  checkReactComponents();
  await checkIndexedDB();
  
  console.log('✅ Debug complete! Check the logs above for issues.');
  console.log('💡 If no trades found, run: addTestTradeData()');
}

// Make functions available globally
window.debugDashboard = {
  checkIndexedDB,
  checkReactComponents,
  checkCurrentState,
  addTestTradeData,
  runAllChecks
};

// Auto-run the checks
runAllChecks();
