/**
 * Test script to verify Recent Trades sorting
 *
 * This script adds test trades to IndexedDB and verifies that the Recent Trades
 * section shows them in the correct order (newest first).
 */
declare const testTrades: {
    trade: {
        id: number;
        date: string;
        market: string;
        direction: string;
        session: string;
        model_type: string;
        entry_price: number;
        exit_price: number;
        no_of_contracts: number;
        achieved_pl: number;
        r_multiple: number;
        win_loss: string;
        pattern_quality_rating: number;
        entry_time: string;
        exit_time: string;
        notes: string;
    };
    fvg_details: null;
    setup: null;
    analysis: null;
}[];
//# sourceMappingURL=test-recent-trades-sorting.d.ts.map