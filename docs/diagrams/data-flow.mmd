graph TD
    packages_dashboard_src_App_tsx[App.tsx]:::component
    packages_dashboard_src_MinimalApp_tsx[MinimalApp.tsx]:::component
    packages_dashboard_src_SimpleApp_tsx[SimpleApp.tsx]:::component
    packages_dashboard_src_TestApp_tsx[TestApp.tsx]:::hook
    packages_dashboard_src_components_AppErrorBoundary_tsx[AppErrorBoundary.tsx]:::hook
    packages_dashboard_src_components_FeatureErrorBoundary_tsx[FeatureErrorBoundary.tsx]:::hook
    packages_dashboard_src_components_NotFound_tsx[NotFound.tsx]:::hook
    packages_dashboard_src_components___tests___AppErrorBoundary_test_d_ts[AppErrorBoundary.test.d.ts]:::component
    packages_dashboard_src_components___tests___AppErrorBoundary_test_js[AppErrorBoundary.test.js]:::component
    packages_dashboard_src_components___tests___AppErrorBoundary_test_tsx[AppErrorBoundary.test.tsx]:::component
    packages_dashboard_src_components___tests___FeatureErrorBoundary_test_d_ts[FeatureErrorBoundary.test.d.ts]:::component
    packages_dashboard_src_components___tests___FeatureErrorBoundary_test_js[FeatureErrorBoundary.test.js]:::component
    packages_dashboard_src_components___tests___FeatureErrorBoundary_test_tsx[FeatureErrorBoundary.test.tsx]:::component
    packages_dashboard_src_components_molecules_LoadingScreen_tsx[LoadingScreen.tsx]:::component
    packages_dashboard_src_components_molecules_ProfitLossCell_stories_tsx[ProfitLossCell.stories.tsx]:::hook
    packages_dashboard_src_components_molecules_ProfitLossCell_tsx[ProfitLossCell.tsx]:::hook
    packages_dashboard_src_devtools_config_js[devtools-config.js]:::component
    packages_dashboard_src_features_daily_guide_DailyGuide_tsx[DailyGuide.tsx]:::hook
    packages_dashboard_src_features_daily_guide___tests___DailyGuide_test_d_ts[DailyGuide.test.d.ts]:::component
    packages_dashboard_src_features_daily_guide___tests___DailyGuide_test_js[DailyGuide.test.js]:::component
    packages_dashboard_src_features_daily_guide___tests___DailyGuide_test_tsx[DailyGuide.test.tsx]:::component
    packages_dashboard_src_features_daily_guide_api_dailyGuideApi_ts[dailyGuideApi.ts]:::component
    packages_dashboard_src_features_daily_guide_components_AddItemForm_tsx[AddItemForm.tsx]:::hook
    packages_dashboard_src_features_daily_guide_components_DailyGuide_tsx[DailyGuide.tsx]:::hook
    packages_dashboard_src_features_daily_guide_components_DailyGuideContainer_tsx[DailyGuideContainer.tsx]:::hook
    packages_dashboard_src_features_daily_guide_components_DailyGuideHeader_tsx[DailyGuideHeader.tsx]:::component
    packages_dashboard_src_features_daily_guide_components_F1GuideContainer_tsx[F1GuideContainer.tsx]:::hook
    packages_dashboard_src_features_daily_guide_components_F1GuideHeader_tsx[F1GuideHeader.tsx]:::hook
    packages_dashboard_src_features_daily_guide_components_F1GuideTabs_tsx[F1GuideTabs.tsx]:::hook
    packages_dashboard_src_features_daily_guide_components_KeyLevels_tsx[KeyLevels.tsx]:::component
    packages_dashboard_src_features_daily_guide_components_MarketIndicators_tsx[MarketIndicators.tsx]:::component
    packages_dashboard_src_features_daily_guide_components_MarketNews_tsx[MarketNews.tsx]:::component
    packages_dashboard_src_features_daily_guide_components_MarketOverview_tsx[MarketOverview.tsx]:::component
    packages_dashboard_src_features_daily_guide_components_MarketSummary_tsx[MarketSummary.tsx]:::component
    packages_dashboard_src_features_daily_guide_components_PlanItemsList_tsx[PlanItemsList.tsx]:::hook
    packages_dashboard_src_features_daily_guide_components_RiskManagementGrid_tsx[RiskManagementGrid.tsx]:::hook
    packages_dashboard_src_features_daily_guide_components_TradingPlan_tsx[TradingPlan.tsx]:::hook
    packages_dashboard_src_features_daily_guide_components_TradingPlanContainer_tsx[TradingPlanContainer.tsx]:::hook
    packages_dashboard_src_features_daily_guide_components_TradingPlanHeader_tsx[TradingPlanHeader.tsx]:::component
    packages_dashboard_src_features_daily_guide_components___tests___DailyGuide_test_d_ts[DailyGuide.test.d.ts]:::component
    packages_dashboard_src_features_daily_guide_components___tests___DailyGuide_test_js[DailyGuide.test.js]:::hook
    packages_dashboard_src_features_daily_guide_components___tests___DailyGuide_test_tsx[DailyGuide.test.tsx]:::hook
    packages_dashboard_src_features_daily_guide_components_f1_guide_components_ts[f1-guide-components.ts]:::hook
    packages_dashboard_src_features_daily_guide_components_guideTabConfig_tsx[guideTabConfig.tsx]:::hook
    packages_dashboard_src_features_daily_guide_components_index_ts[index.ts]:::component
    packages_dashboard_src_features_daily_guide_components_ui_PriorityTag_tsx[PriorityTag.tsx]:::component
    packages_dashboard_src_features_daily_guide_components_ui_SectionCard_tsx[SectionCard.tsx]:::component
    packages_dashboard_src_features_daily_guide_components_ui_SentimentBadge_tsx[SentimentBadge.tsx]:::component
    packages_dashboard_src_features_daily_guide_components_ui_index_ts[index.ts]:::component
    packages_dashboard_src_features_daily_guide_components_useGuideNavigation_ts[useGuideNavigation.ts]:::hook
    packages_dashboard_src_features_daily_guide_context_DailyGuideContext_tsx[DailyGuideContext.tsx]:::context
    packages_dashboard_src_features_daily_guide_hooks___tests___useDailyGuide_test_d_ts[useDailyGuide.test.d.ts]:::hook
    packages_dashboard_src_features_daily_guide_hooks___tests___useDailyGuide_test_js[useDailyGuide.test.js]:::hook
    packages_dashboard_src_features_daily_guide_hooks___tests___useDailyGuide_test_tsx[useDailyGuide.test.tsx]:::hook
    packages_dashboard_src_features_daily_guide_hooks_index_ts[index.ts]:::hook
    packages_dashboard_src_features_daily_guide_hooks_useDailyGuide_ts[useDailyGuide.ts]:::hook
    packages_dashboard_src_features_daily_guide_hooks_useTradingPlanForm_ts[useTradingPlanForm.ts]:::hook
    packages_dashboard_src_features_daily_guide_index_ts[index.ts]:::hook
    packages_dashboard_src_features_daily_guide_state_dailyGuideSelectors_ts[dailyGuideSelectors.ts]:::component
    packages_dashboard_src_features_daily_guide_state_dailyGuideState_ts[dailyGuideState.ts]:::hook
    packages_dashboard_src_features_daily_guide_state_index_ts[index.ts]:::component
    packages_dashboard_src_features_daily_guide_types_data_ts[data.ts]:::hook
    packages_dashboard_src_features_daily_guide_types_index_ts[index.ts]:::hook
    packages_dashboard_src_features_daily_guide_types_market_ts[market.ts]:::hook
    packages_dashboard_src_features_daily_guide_types_preferences_ts[preferences.ts]:::hook
    packages_dashboard_src_features_daily_guide_types_trading_ts[trading.ts]:::hook
    packages_dashboard_src_features_daily_guide_types_ts[types.ts]:::hook
    packages_dashboard_src_features_performance_dashboard_Dashboard_tsx[Dashboard.tsx]:::hook
    packages_dashboard_src_features_performance_dashboard_components_MetricsPanel_tsx[MetricsPanel.tsx]:::component
    packages_dashboard_src_features_performance_dashboard_components_PerformanceChart_tsx[PerformanceChart.tsx]:::hook
    packages_dashboard_src_features_performance_dashboard_components_RecentTradesPanel_tsx[RecentTradesPanel.tsx]:::component
    packages_dashboard_src_features_performance_dashboard_hooks_useDashboardData_ts[useDashboardData.ts]:::hook
    packages_dashboard_src_features_performance_dashboard_index_ts[index.ts]:::component
    packages_dashboard_src_features_settings_Settings_tsx[Settings.tsx]:::hook
    packages_dashboard_src_features_settings_components_SettingItem_tsx[SettingItem.tsx]:::component
    packages_dashboard_src_features_settings_components_SettingsContainer_tsx[SettingsContainer.tsx]:::hook
    packages_dashboard_src_features_settings_components_SettingsForm_tsx[SettingsForm.tsx]:::hook
    packages_dashboard_src_features_settings_components_SettingsFormField_tsx[SettingsFormField.tsx]:::hook
    packages_dashboard_src_features_settings_components_SettingsHeader_tsx[SettingsHeader.tsx]:::hook
    packages_dashboard_src_features_settings_components_SettingsSection_tsx[SettingsSection.tsx]:::component
    packages_dashboard_src_features_settings_components_ToggleSwitch_tsx[ToggleSwitch.tsx]:::component
    packages_dashboard_src_features_settings_components_index_ts[index.ts]:::hook
    packages_dashboard_src_features_settings_hooks_useSettings_ts[useSettings.ts]:::hook
    packages_dashboard_src_features_settings_hooks_useSettingsForm_ts[useSettingsForm.ts]:::hook
    packages_dashboard_src_features_settings_index_ts[index.ts]:::hook
    packages_dashboard_src_features_trade_analysis_TradeAnalysis_tsx[TradeAnalysis.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_AnalysisHeader_tsx[AnalysisHeader.tsx]:::component
    packages_dashboard_src_features_trade_analysis_components_AnalysisTabs_tsx[AnalysisTabs.tsx]:::component
    packages_dashboard_src_features_trade_analysis_components_CategoryPerformanceChart_tsx[CategoryPerformanceChart.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_DistributionChart_tsx[DistributionChart.tsx]:::component
    packages_dashboard_src_features_trade_analysis_components_EquityCurve_tsx[EquityCurve.tsx]:::component
    packages_dashboard_src_features_trade_analysis_components_F1AnalysisContainer_tsx[F1AnalysisContainer.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_F1AnalysisHeader_tsx[F1AnalysisHeader.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_F1AnalysisTabs_tsx[F1AnalysisTabs.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_FilterPanel_tsx[FilterPanel.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_MetricsPanel_tsx[MetricsPanel.tsx]:::component
    packages_dashboard_src_features_trade_analysis_components_PerformanceSummary_tsx[PerformanceSummary.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_TabContentRenderer_tsx[TabContentRenderer.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_TimePerformanceChart_tsx[TimePerformanceChart.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_TradeAnalysis_tsx[TradeAnalysis.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_TradeAnalysisCharts_tsx[TradeAnalysisCharts.tsx]:::component
    packages_dashboard_src_features_trade_analysis_components_TradeAnalysisContainer_tsx[TradeAnalysisContainer.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_TradeAnalysisFilter_tsx[TradeAnalysisFilter.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_TradeAnalysisSummary_tsx[TradeAnalysisSummary.tsx]:::component
    packages_dashboard_src_features_trade_analysis_components_TradeAnalysisTable_tsx[TradeAnalysisTable.tsx]:::component
    packages_dashboard_src_features_trade_analysis_components_TradeDetail_tsx[TradeDetail.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_TradesTable_tsx[TradesTable.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_TradesTableBody_tsx[TradesTableBody.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_TradesTableContainer_tsx[TradesTableContainer.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_TradesTableHeader_tsx[TradesTableHeader.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_TradesTableRow_tsx[TradesTableRow.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_analysisTabConfig_tsx[analysisTabConfig.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_f1_analysis_components_ts[f1-analysis-components.ts]:::hook
    packages_dashboard_src_features_trade_analysis_components_index_ts[index.ts]:::component
    packages_dashboard_src_features_trade_analysis_components_trade_tests_TradeAnalysis_test_d_ts[TradeAnalysis.test.d.ts]:::component
    packages_dashboard_src_features_trade_analysis_components_trade_tests_TradeAnalysis_test_js[TradeAnalysis.test.js]:::hook
    packages_dashboard_src_features_trade_analysis_components_trade_tests_TradeAnalysis_test_tsx[TradeAnalysis.test.tsx]:::hook
    packages_dashboard_src_features_trade_analysis_components_useAnalysisNavigation_ts[useAnalysisNavigation.ts]:::hook
    packages_dashboard_src_features_trade_analysis_hooks_TradeAnalysisContext_tsx[TradeAnalysisContext.tsx]:::context
    packages_dashboard_src_features_trade_analysis_hooks_tradeAnalysisState_ts[tradeAnalysisState.ts]:::hook
    packages_dashboard_src_features_trade_analysis_hooks_useTradeAnalysis_ts[useTradeAnalysis.ts]:::hook
    packages_dashboard_src_features_trade_analysis_hooks_useTradesTableData_ts[useTradesTableData.ts]:::hook
    packages_dashboard_src_features_trade_analysis_index_ts[index.ts]:::hook
    packages_dashboard_src_features_trade_analysis_services_tradeAnalysisApi_ts[tradeAnalysisApi.ts]:::service
    packages_dashboard_src_features_trade_analysis_trade_tests_TradeAnalysis_test_d_ts[TradeAnalysis.test.d.ts]:::component
    packages_dashboard_src_features_trade_analysis_trade_tests_TradeAnalysis_test_js[TradeAnalysis.test.js]:::component
    packages_dashboard_src_features_trade_analysis_trade_tests_TradeAnalysis_test_tsx[TradeAnalysis.test.tsx]:::component
    packages_dashboard_src_features_trade_analysis_types_index_ts[index.ts]:::component
    packages_dashboard_src_features_trade_analysis_types_ts[types.ts]:::hook
    packages_dashboard_src_features_trade_entry_components_SetupBuilder_tsx[SetupBuilder.tsx]:::hook
    packages_dashboard_src_features_trade_journal_TradeForm_test_d_ts[TradeForm.test.d.ts]:::component
    packages_dashboard_src_features_trade_journal_TradeForm_test_js[TradeForm.test.js]:::hook
    packages_dashboard_src_features_trade_journal_TradeForm_test_tsx[TradeForm.test.tsx]:::hook
    packages_dashboard_src_features_trade_journal_TradeForm_tsx[TradeForm.tsx]:::hook
    packages_dashboard_src_features_trade_journal_TradeJournal_tsx[TradeJournal.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_F1FilterField_tsx[F1FilterField.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_F1FilterPanel_tsx[F1FilterPanel.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_F1JournalContainer_tsx[F1JournalContainer.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_F1JournalHeader_tsx[F1JournalHeader.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_F1JournalTabs_tsx[F1JournalTabs.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_LegacyDataImport_jsx[LegacyDataImport.jsx]:::hook
    packages_dashboard_src_features_trade_journal_components_SelectDropdown_tsx[SelectDropdown.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_TabPanel_test_d_ts[TabPanel.test.d.ts]:::component
    packages_dashboard_src_features_trade_journal_components_TabPanel_test_js[TabPanel.test.js]:::component
    packages_dashboard_src_features_trade_journal_components_TabPanel_test_tsx[TabPanel.test.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_TabPanel_tsx[TabPanel.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_TimePicker_tsx[TimePicker.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_TradeList_tsx[TradeList.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_f1_filter_components_ts[f1-filter-components.ts]:::hook
    packages_dashboard_src_features_trade_journal_components_f1_journal_components_ts[f1-journal-components.ts]:::hook
    packages_dashboard_src_features_trade_journal_components_filterFieldConfig_tsx[filterFieldConfig.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_index_ts[index.ts]:::component
    packages_dashboard_src_features_trade_journal_components_journalTabConfig_tsx[journalTabConfig.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_analysis_section_TradeAnalysisSection_tsx[TradeAnalysisSection.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_analysis_section_index_ts[index.ts]:::component
    packages_dashboard_src_features_trade_journal_components_trade_dol_analysis_DOLContextSelector_tsx[DOLContextSelector.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_dol_analysis_DOLDetailedAnalysis_tsx[DOLDetailedAnalysis.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_dol_analysis_DOLEffectivenessRating_tsx[DOLEffectivenessRating.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_dol_analysis_DOLReactionSelector_tsx[DOLReactionSelector.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_dol_analysis_DOLStrengthSelector_tsx[DOLStrengthSelector.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_dol_analysis_DOLTypeSelector_tsx[DOLTypeSelector.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_dol_analysis_TradeDOLAnalysis_tsx[TradeDOLAnalysis.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_dol_analysis_index_ts[index.ts]:::component
    packages_dashboard_src_features_trade_journal_components_trade_form_F1TradeFormField_tsx[F1TradeFormField.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_form_TradeFormActions_tsx[TradeFormActions.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_form_TradeFormBasicFields_tsx[TradeFormBasicFields.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_form_TradeFormBasicFieldsContainer_tsx[TradeFormBasicFieldsContainer.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_form_TradeFormFieldGroups_tsx[TradeFormFieldGroups.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_form_TradeFormHeader_tsx[TradeFormHeader.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_form_TradeFormLoading_tsx[TradeFormLoading.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_form_TradeFormMessages_tsx[TradeFormMessages.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_form_TradeFormRiskFields_tsx[TradeFormRiskFields.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_form_TradeFormStrategyFields_tsx[TradeFormStrategyFields.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_form_TradeFormTimingFields_tsx[TradeFormTimingFields.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_form_f1_components_ts[f1-components.ts]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_form_index_ts[index.ts]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_form_tradeFormFieldConfig_ts[tradeFormFieldConfig.ts]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_form_useTradeFormFields_ts[useTradeFormFields.ts]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_journal_TradeJournalContent_tsx[TradeJournalContent.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_journal_TradeJournalFilters_tsx[TradeJournalFilters.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_journal_TradeJournalHeader_tsx[TradeJournalHeader.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_journal_index_ts[index.ts]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_list_TradeListEmpty_tsx[TradeListEmpty.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_list_TradeListExpandedRow_tsx[TradeListExpandedRow.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_list_TradeListHeader_tsx[TradeListHeader.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_list_TradeListLoading_tsx[TradeListLoading.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_list_TradeListRow_tsx[TradeListRow.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_list_index_ts[index.ts]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_pattern_quality_CriterionSelector_tsx[CriterionSelector.tsx]:::component
    packages_dashboard_src_features_trade_journal_components_trade_pattern_quality_PatternQualityAssessment_tsx[PatternQualityAssessment.tsx]:::hook
    packages_dashboard_src_features_trade_journal_components_trade_pattern_quality_index_ts[index.ts]:::component
    packages_dashboard_src_features_trade_journal_components_useFilterState_ts[useFilterState.ts]:::hook
    packages_dashboard_src_features_trade_journal_components_useJournalNavigation_ts[useJournalNavigation.ts]:::hook
    packages_dashboard_src_features_trade_journal_constants_dolAnalysis_ts[dolAnalysis.ts]:::component
    packages_dashboard_src_features_trade_journal_constants_patternQuality_ts[patternQuality.ts]:::component
    packages_dashboard_src_features_trade_journal_constants_setupClassification_ts[setupClassification.ts]:::component
    packages_dashboard_src_features_trade_journal_hooks_index_ts[index.ts]:::hook
    packages_dashboard_src_features_trade_journal_hooks_useTradeCalculations_ts[useTradeCalculations.ts]:::hook
    packages_dashboard_src_features_trade_journal_hooks_useTradeFilters_ts[useTradeFilters.ts]:::hook
    packages_dashboard_src_features_trade_journal_hooks_useTradeForm_ts[useTradeForm.ts]:::hook
    packages_dashboard_src_features_trade_journal_hooks_useTradeFormData_ts[useTradeFormData.ts]:::hook
    packages_dashboard_src_features_trade_journal_hooks_useTradeJournal_ts[useTradeJournal.ts]:::hook
    packages_dashboard_src_features_trade_journal_hooks_useTradeList_ts[useTradeList.ts]:::hook
    packages_dashboard_src_features_trade_journal_hooks_useTradeSubmission_ts[useTradeSubmission.ts]:::hook
    packages_dashboard_src_features_trade_journal_hooks_useTradeValidation_ts[useTradeValidation.ts]:::hook
    packages_dashboard_src_features_trade_journal_index_ts[index.ts]:::component
    packages_dashboard_src_features_trade_journal_tests_useTradeSubmission_test_d_ts[useTradeSubmission.test.d.ts]:::hook
    packages_dashboard_src_features_trade_journal_tests_useTradeSubmission_test_js[useTradeSubmission.test.js]:::hook
    packages_dashboard_src_features_trade_journal_tests_useTradeSubmission_test_ts[useTradeSubmission.test.ts]:::hook
    packages_dashboard_src_features_trade_journal_types_index_ts[index.ts]:::hook
    packages_dashboard_src_features_trading_dashboard_TradingDashboard_tsx[TradingDashboard.tsx]:::hook
    packages_dashboard_src_features_trading_dashboard_components_DashboardTabs_tsx[DashboardTabs.tsx]:::hook
    packages_dashboard_src_features_trading_dashboard_components_F1DashboardContainer_tsx[F1DashboardContainer.tsx]:::hook
    packages_dashboard_src_features_trading_dashboard_components_F1DashboardHeader_tsx[F1DashboardHeader.tsx]:::hook
    packages_dashboard_src_features_trading_dashboard_components_F1DashboardTabs_tsx[F1DashboardTabs.tsx]:::hook
    packages_dashboard_src_features_trading_dashboard_components_F1Header_tsx[F1Header.tsx]:::component
    packages_dashboard_src_features_trading_dashboard_components_MetricsPanel_tsx[MetricsPanel.tsx]:::component
    packages_dashboard_src_features_trading_dashboard_components_PerformanceChart_tsx[PerformanceChart.tsx]:::component
    packages_dashboard_src_features_trading_dashboard_components_QuickTradeForm_tsx[QuickTradeForm.tsx]:::hook
    packages_dashboard_src_features_trading_dashboard_components_QuickTradeFormActions_tsx[QuickTradeFormActions.tsx]:::component
    packages_dashboard_src_features_trading_dashboard_components_QuickTradeFormContainer_tsx[QuickTradeFormContainer.tsx]:::hook
    packages_dashboard_src_features_trading_dashboard_components_QuickTradeFormFields_tsx[QuickTradeFormFields.tsx]:::component
    packages_dashboard_src_features_trading_dashboard_components_RecentTradesTable_tsx[RecentTradesTable.tsx]:::component
    packages_dashboard_src_features_trading_dashboard_components_SetupAnalysis_tsx[SetupAnalysis.tsx]:::component
    packages_dashboard_src_features_trading_dashboard_components_TradingDashboardContainer_tsx[TradingDashboardContainer.tsx]:::hook
    packages_dashboard_src_features_trading_dashboard_components_dashboardTabConfig_tsx[dashboardTabConfig.tsx]:::hook
    packages_dashboard_src_features_trading_dashboard_components_f1_dashboard_components_ts[f1-dashboard-components.ts]:::hook
    packages_dashboard_src_features_trading_dashboard_components_index_ts[index.ts]:::hook
    packages_dashboard_src_features_trading_dashboard_components_useDashboardNavigation_ts[useDashboardNavigation.ts]:::hook
    packages_dashboard_src_features_trading_dashboard_context_TradingDashboardContext_tsx[TradingDashboardContext.tsx]:::context
    packages_dashboard_src_features_trading_dashboard_hooks_useQuickTradeForm_ts[useQuickTradeForm.ts]:::hook
    packages_dashboard_src_features_trading_dashboard_hooks_useTradingDashboard_ts[useTradingDashboard.ts]:::hook
    packages_dashboard_src_features_trading_dashboard_hooks_useTradingDashboardData_ts[useTradingDashboardData.ts]:::hook
    packages_dashboard_src_features_trading_dashboard_index_ts[index.ts]:::hook
    packages_dashboard_src_features_trading_dashboard_types_index_ts[index.ts]:::hook
    packages_dashboard_src_features_trading_dashboard_utils_dataValidation_ts[dataValidation.ts]:::hook
    packages_dashboard_src_index_tsx[index.tsx]:::component
    packages_dashboard_src_layouts_Header_tsx[Header.tsx]:::component
    packages_dashboard_src_layouts_MainLayout_tsx[MainLayout.tsx]:::hook
    packages_dashboard_src_layouts_Sidebar_tsx[Sidebar.tsx]:::hook
    packages_dashboard_src_layouts_index_ts[index.ts]:::component
    packages_dashboard_src_pages_DailyGuide_tsx[DailyGuide.tsx]:::component
    packages_dashboard_src_pages_Dashboard_tsx[Dashboard.tsx]:::component
    packages_dashboard_src_pages_NotFound_tsx[NotFound.tsx]:::component
    packages_dashboard_src_pages_Settings_tsx[Settings.tsx]:::component
    packages_dashboard_src_pages_TradeAnalysis_tsx[TradeAnalysis.tsx]:::hook
    packages_dashboard_src_pages_TradeForm_tsx[TradeForm.tsx]:::hook
    packages_dashboard_src_pages_TradeJournal_tsx[TradeJournal.tsx]:::component
    packages_dashboard_src_reportWebVitals_ts[reportWebVitals.ts]:::component
    packages_dashboard_src_routes_components_molecules_LoadingScreen_tsx[LoadingScreen.tsx]:::component
    packages_dashboard_src_routes_index_ts[index.ts]:::component
    packages_dashboard_src_routes_index_tsx[index.tsx]:::component
    packages_dashboard_src_routes_layouts_MainLayout_tsx[MainLayout.tsx]:::component
    packages_dashboard_src_routes_routes_test_d_ts[routes.test.d.ts]:::component
    packages_dashboard_src_routes_routes_test_js[routes.test.js]:::component
    packages_dashboard_src_routes_routes_test_tsx[routes.test.tsx]:::component
    packages_dashboard_src_routes_routes_tsx[routes.tsx]:::component
    packages_dashboard_src_services_contracts_TradeJournalApiImpl_ts[TradeJournalApiImpl.ts]:::service
    packages_dashboard_src_services_transformers_setupTransformer_ts[setupTransformer.ts]:::service
    packages_dashboard_src_simple_index_tsx[simple-index.tsx]:::component
    packages_shared_src_api_context_index_ts[index.ts]:::context
    packages_shared_src_api_index_ts[index.ts]:::service
    packages_shared_src_components_atoms_Badge_stories_tsx[Badge.stories.tsx]:::component
    packages_shared_src_components_atoms_Badge_tsx[Badge.tsx]:::component
    packages_shared_src_components_atoms_Button_tsx[Button.tsx]:::component
    packages_shared_src_components_atoms_Input_stories_tsx[Input.stories.tsx]:::hook
    packages_shared_src_components_atoms_Input_tsx[Input.tsx]:::hook
    packages_shared_src_components_atoms_LoadingCell_tsx[LoadingCell.tsx]:::component
    packages_shared_src_components_atoms_LoadingPlaceholder_tsx[LoadingPlaceholder.tsx]:::hook
    packages_shared_src_components_atoms_LoadingSpinner_tsx[LoadingSpinner.tsx]:::component
    packages_shared_src_components_atoms_Select_stories_tsx[Select.stories.tsx]:::hook
    packages_shared_src_components_atoms_Select_tsx[Select.tsx]:::hook
    packages_shared_src_components_atoms_SelectDropdown_tsx[SelectDropdown.tsx]:::component
    packages_shared_src_components_atoms_StatusIndicator_tsx[StatusIndicator.tsx]:::component
    packages_shared_src_components_atoms_Tag_stories_tsx[Tag.stories.tsx]:::component
    packages_shared_src_components_atoms_Tag_tsx[Tag.tsx]:::hook
    packages_shared_src_components_atoms_TimePicker_tsx[TimePicker.tsx]:::component
    packages_shared_src_components_atoms_index_ts[index.ts]:::component
    packages_shared_src_components_base_tsx[base.tsx]:::hook
    packages_shared_src_components_hooks_index_ts[index.ts]:::hook
    packages_shared_src_components_hooks_useFormField_ts[useFormField.ts]:::hook
    packages_shared_src_components_index_ts[index.ts]:::component
    packages_shared_src_components_library_containers_F1Container_tsx[F1Container.tsx]:::component
    packages_shared_src_components_library_forms_F1Form_tsx[F1Form.tsx]:::component
    packages_shared_src_components_library_forms_F1FormField_tsx[F1FormField.tsx]:::hook
    packages_shared_src_components_library_headers_F1Header_tsx[F1Header.tsx]:::component
    packages_shared_src_components_library_index_full_ts[index.full.ts]:::hook
    packages_shared_src_components_library_index_ts[index.ts]:::hook
    packages_shared_src_components_molecules_Card_stories_tsx[Card.stories.tsx]:::component
    packages_shared_src_components_molecules_Card_tsx[Card.tsx]:::component
    packages_shared_src_components_molecules_EmptyState_tsx[EmptyState.tsx]:::component
    packages_shared_src_components_molecules_EnhancedFormField_tsx[EnhancedFormField.tsx]:::hook
    packages_shared_src_components_molecules_ErrorBoundary_tsx[ErrorBoundary.tsx]:::hook
    packages_shared_src_components_molecules_FormField_tsx[FormField.tsx]:::component
    packages_shared_src_components_molecules_HierarchicalSessionSelector_tsx[HierarchicalSessionSelector.tsx]:::hook
    packages_shared_src_components_molecules_Modal_stories_tsx[Modal.stories.tsx]:::hook
    packages_shared_src_components_molecules_Modal_tsx[Modal.tsx]:::hook
    packages_shared_src_components_molecules_SortableTable_tsx[SortableTable.tsx]:::hook
    packages_shared_src_components_molecules_TabPanel_tsx[TabPanel.tsx]:::hook
    packages_shared_src_components_molecules_Table_stories_tsx[Table.stories.tsx]:::hook
    packages_shared_src_components_molecules_Table_tsx[Table.tsx]:::hook
    packages_shared_src_components_molecules_TradeTable_example_tsx[TradeTable.example.tsx]:::hook
    packages_shared_src_components_molecules_TradeTable_tsx[TradeTable.tsx]:::hook
    packages_shared_src_components_molecules_TradeTableColumns_tsx[TradeTableColumns.tsx]:::hook
    packages_shared_src_components_molecules_TradeTableFilters_tsx[TradeTableFilters.tsx]:::component
    packages_shared_src_components_molecules_TradeTableRow_tsx[TradeTableRow.tsx]:::hook
    packages_shared_src_components_molecules_UnifiedErrorBoundary_tsx[UnifiedErrorBoundary.tsx]:::hook
    packages_shared_src_components_molecules___tests___ErrorBoundary_test_d_ts[ErrorBoundary.test.d.ts]:::component
    packages_shared_src_components_molecules___tests___ErrorBoundary_test_js[ErrorBoundary.test.js]:::component
    packages_shared_src_components_molecules___tests___ErrorBoundary_test_tsx[ErrorBoundary.test.tsx]:::component
    packages_shared_src_components_molecules___tests___UnifiedErrorBoundary_test_d_ts[UnifiedErrorBoundary.test.d.ts]:::component
    packages_shared_src_components_molecules___tests___UnifiedErrorBoundary_test_js[UnifiedErrorBoundary.test.js]:::hook
    packages_shared_src_components_molecules___tests___UnifiedErrorBoundary_test_tsx[UnifiedErrorBoundary.test.tsx]:::hook
    packages_shared_src_components_molecules_index_ts[index.ts]:::component
    packages_shared_src_components_organisms_DashboardSection_tsx[DashboardSection.tsx]:::hook
    packages_shared_src_components_organisms_DataCard_stories_tsx[DataCard.stories.tsx]:::component
    packages_shared_src_components_organisms_DataCard_tsx[DataCard.tsx]:::hook
    packages_shared_src_components_organisms_index_ts[index.ts]:::component
    packages_shared_src_components_templates_DashboardTemplate_stories_tsx[DashboardTemplate.stories.tsx]:::component
    packages_shared_src_components_templates_DashboardTemplate_tsx[DashboardTemplate.tsx]:::hook
    packages_shared_src_components_templates_index_ts[index.ts]:::component
    packages_shared_src_components_trade_SetupBuilder_tsx[SetupBuilder.tsx]:::hook
    packages_shared_src_components_trade_TradeAnalysis_tsx[TradeAnalysis.tsx]:::hook
    packages_shared_src_components_trade_TradeMetrics_tsx[TradeMetrics.tsx]:::component
    packages_shared_src_components_trade_index_ts[index.ts]:::hook
    packages_shared_src_components_trade_types_ts[types.ts]:::component
    packages_shared_src_config_tradingSessionsConfig_ts[tradingSessionsConfig.ts]:::component
    packages_shared_src_constants_index_ts[index.ts]:::hook
    packages_shared_src_constants_setupElements_ts[setupElements.ts]:::component
    packages_shared_src_contracts_TradeAnalysisContract_ts[TradeAnalysisContract.ts]:::component
    packages_shared_src_contracts_TradeJournalContract_ts[TradeJournalContract.ts]:::hook
    packages_shared_src_contracts_TradingDashboardContract_ts[TradingDashboardContract.ts]:::component
    packages_shared_src_contracts_index_ts[index.ts]:::component
    packages_shared_src_hooks___tests___useErrorHandler_test_d_ts[useErrorHandler.test.d.ts]:::hook
    packages_shared_src_hooks___tests___useErrorHandler_test_js[useErrorHandler.test.js]:::hook
    packages_shared_src_hooks___tests___useErrorHandler_test_tsx[useErrorHandler.test.tsx]:::hook
    packages_shared_src_hooks_index_ts[index.ts]:::hook
    packages_shared_src_hooks_useAsyncData_ts[useAsyncData.ts]:::hook
    packages_shared_src_hooks_useDataFormatting_ts[useDataFormatting.ts]:::hook
    packages_shared_src_hooks_useDataSection_ts[useDataSection.ts]:::hook
    packages_shared_src_hooks_useDebounce_ts[useDebounce.ts]:::hook
    packages_shared_src_hooks_useErrorHandler_ts[useErrorHandler.ts]:::hook
    packages_shared_src_hooks_useFormField_ts[useFormField.ts]:::hook
    packages_shared_src_hooks_useLoadingState_ts[useLoadingState.ts]:::hook
    packages_shared_src_hooks_useLocalStorage_ts[useLocalStorage.ts]:::hook
    packages_shared_src_hooks_usePagination_ts[usePagination.ts]:::hook
    packages_shared_src_hooks_useProfitLossFormatting_ts[useProfitLossFormatting.ts]:::hook
    packages_shared_src_hooks_useSessionSelection_ts[useSessionSelection.ts]:::hook
    packages_shared_src_hooks_useSortableTable_ts[useSortableTable.ts]:::hook
    packages_shared_src_index_ts[index.ts]:::hook
    packages_shared_src_monitoring_index_ts[index.ts]:::hook
    packages_shared_src_react_types_d_ts[react-types.d.ts]:::component
    packages_shared_src_services___tests___tradeStorage_test_d_ts[tradeStorage.test.d.ts]:::service
    packages_shared_src_services___tests___tradeStorage_test_js[tradeStorage.test.js]:::hook
    packages_shared_src_services___tests___tradeStorage_test_ts[tradeStorage.test.ts]:::hook
    packages_shared_src_services_index_ts[index.ts]:::service
    packages_shared_src_services_persistState_ts[persistState.ts]:::hook
    packages_shared_src_services_tradeStorage_ts[tradeStorage.ts]:::service
    packages_shared_src_services_tradeStorageInterface_ts[tradeStorageInterface.ts]:::service
    packages_shared_src_state___tests___createStoreContext_test_d_ts[createStoreContext.test.d.ts]:::component
    packages_shared_src_state___tests___createStoreContext_test_js[createStoreContext.test.js]:::hook
    packages_shared_src_state___tests___createStoreContext_test_tsx[createStoreContext.test.tsx]:::hook
    packages_shared_src_state_createSelector_ts[createSelector.ts]:::component
    packages_shared_src_state_createStoreContext_tsx[createStoreContext.tsx]:::context
    packages_shared_src_state_index_ts[index.ts]:::component
    packages_shared_src_styled_d_ts[styled.d.ts]:::component
    packages_shared_src_theme_GlobalStyles_tsx[GlobalStyles.tsx]:::hook
    packages_shared_src_theme_ThemeProvider_tsx[ThemeProvider.tsx]:::context
    packages_shared_src_theme_darkTheme_ts[darkTheme.ts]:::component
    packages_shared_src_theme_f1Theme_ts[f1Theme.ts]:::component
    packages_shared_src_theme_index_ts[index.ts]:::component
    packages_shared_src_theme_lightTheme_ts[lightTheme.ts]:::component
    packages_shared_src_theme_profitLossTheme_ts[profitLossTheme.ts]:::component
    packages_shared_src_theme_theme_types_ts[theme.types.ts]:::component
    packages_shared_src_theme_tokens_colors_ts[colors.ts]:::hook
    packages_shared_src_theme_tokens_index_ts[index.ts]:::hook
    packages_shared_src_theme_tokens_spacing_ts[spacing.ts]:::hook
    packages_shared_src_theme_tokens_typography_ts[typography.ts]:::hook
    packages_shared_src_theme_tokens_ts[tokens.ts]:::component
    packages_shared_src_theme_types_ts[types.ts]:::component
    packages_shared_src_theme_variants_f1Theme_ts[f1Theme.ts]:::component
    packages_shared_src_theme_variants_index_ts[index.ts]:::component
    packages_shared_src_theme_variants_lightTheme_ts[lightTheme.ts]:::component
    packages_shared_src_types_index_ts[index.ts]:::component
    packages_shared_src_types_trading_ts[trading.ts]:::component
    packages_shared_src_types_tradingSessions_ts[tradingSessions.ts]:::component
    packages_shared_src_utils_index_ts[index.ts]:::component
    packages_shared_src_utils_sessionMigration_ts[sessionMigration.ts]:::component
    packages_shared_src_utils_sessionUtils_ts[sessionUtils.ts]:::hook

    classDef service fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef context fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef hook fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef component fill:#fff3e0,stroke:#e65100,stroke-width:1px
    classDef bottleneck fill:#ffebee,stroke:#c62828,stroke-width:3px