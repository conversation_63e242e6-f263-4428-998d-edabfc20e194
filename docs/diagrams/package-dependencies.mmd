graph LR
    subgraph "External Dependencies"
        React[React 18.2.0]
        StyledComponents[Styled Components 5.3.6]
        ReactRouter[React Router 6.6.2]
        Recharts[Recharts 2.10.3]
        IDB[IndexedDB API]
    end

    subgraph "Shared Package"
        SharedComponents[Components]
        SharedHooks[Hooks]
        SharedServices[Services]
        SharedTheme[Theme]
        SharedState[State Management]
        SharedUtils[Utilities]
    end

    subgraph "Dashboard Package"
        TradingDashboard[Trading Dashboard]
        TradeJournal[Trade Journal]
        TradeAnalysis[Trade Analysis]
        DailyGuide[Daily Guide]
        Settings[Settings]
    end

    React --> SharedComponents
    StyledComponents --> SharedTheme
    IDB --> SharedServices

    SharedComponents --> TradingDashboard
    SharedHooks --> TradeJournal
    SharedServices --> TradeAnalysis
    SharedState --> DailyGuide
    SharedUtils --> Settings

    ReactRouter --> TradingDashboard
    Recharts --> TradeAnalysis

    classDef external fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef shared fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef dashboard fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px

    class React,StyledComponents,ReactRouter,Recharts,IDB external
    class SharedComponents,SharedHooks,SharedServices,SharedTheme,SharedState,SharedUtils shared
    class TradingDashboard,TradeJournal,TradeAnalysis,DailyGuide,Settings dashboard