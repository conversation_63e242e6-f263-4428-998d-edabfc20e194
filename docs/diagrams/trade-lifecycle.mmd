graph LR
    A[User Input] --> B[TradeForm Component]
    B --> C[useTradeSubmission Hook]
    C --> D[TradeStorageService]
    D --> E[IndexedDB]

    E --> F[Data Retrieval]
    F --> G[useTradingDashboard Hook]
    G --> H[TradingDashboardContext]
    H --> I[Dashboard Components]

    E --> J[Trade Journal]
    J --> K[useTradeJournal Hook]
    K --> L[TradeList Component]

    subgraph "Storage Layer"
        D
        E
    end

    subgraph "State Management"
        C
        G
        H
        K
    end

    subgraph "UI Layer"
        B
        I
        L
    end

    classDef storage fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef state fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef ui fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px

    class D,E storage
    class C,G,H,K state
    class B,I,L ui