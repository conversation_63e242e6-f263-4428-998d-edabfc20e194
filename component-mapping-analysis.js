/**
 * Component Mapping Analysis
 * 
 * This script maps all trade display components and their data sources
 */

console.log('🗺️ COMPONENT MAPPING ANALYSIS');
console.log('='.repeat(50));

// Component mapping based on codebase analysis
const TRADE_DISPLAY_COMPONENTS = {
  'RecentTradesTable': {
    location: 'packages/dashboard/src/features/trading-dashboard/components/RecentTradesTable.tsx',
    usedIn: ['TradingDashboardContainer (Summary tab)', 'TradingDashboardContainer (Trades tab)'],
    dataSource: 'useTradingDashboardData → DataTransformers.convertToTradeFormat',
    columns: ['Date', 'Setup', 'Session', 'Direction', 'Market', 'Entry', 'Exit', 'R-Multiple', 'P&L'],
    dataType: 'DashboardTrade[]',
    sorting: 'Applied in DataTransformers.convertToTradeFormat (our fix)',
    route: '/ (main dashboard)'
  },
  
  'RecentTradesPanel': {
    location: 'packages/dashboard/src/features/performance-dashboard/components/RecentTradesPanel.tsx',
    usedIn: ['Performance Dashboard'],
    dataSource: 'useDashboardData → direct CompleteTradeData',
    columns: ['Date', 'Symbol', 'Direction', 'Result', 'Profit/Loss'],
    dataType: 'CompleteTradeData[]',
    sorting: 'NOT APPLIED - uses raw data',
    route: '/performance (if exists)'
  },
  
  'TradeList': {
    location: 'packages/dashboard/src/features/trade-journal/components/TradeList.tsx',
    usedIn: ['Trade Journal', 'Journal Tab Content'],
    dataSource: 'useTradeList → sortedTrades (has its own sorting)',
    columns: ['Expandable rows with full trade details'],
    dataType: 'CompleteTradeData[]',
    sorting: 'Applied in useTradeList hook (newest first)',
    route: '/journal or journal tabs'
  },
  
  'TradesTable': {
    location: 'packages/dashboard/src/features/trade-analysis/components/TradesTable.tsx',
    usedIn: ['Trade Analysis'],
    dataSource: 'useTradeAnalysis → TradesTableContainer',
    columns: ['Analysis-specific columns'],
    dataType: 'CompleteTradeData[]',
    sorting: 'Applied in useTradesTableData',
    route: '/analysis'
  }
};

function analyzeComponentMapping() {
  console.log('\n📊 COMPONENT ANALYSIS:');
  
  Object.entries(TRADE_DISPLAY_COMPONENTS).forEach(([name, info]) => {
    console.log(`\n🎯 ${name}:`);
    console.log(`   📍 Location: ${info.location}`);
    console.log(`   🔗 Used in: ${info.usedIn.join(', ')}`);
    console.log(`   📊 Data source: ${info.dataSource}`);
    console.log(`   📋 Columns: ${info.columns.join(', ')}`);
    console.log(`   🔢 Data type: ${info.dataType}`);
    console.log(`   🔄 Sorting: ${info.sorting}`);
    console.log(`   🌐 Route: ${info.route}`);
  });
}

function identifyProblemComponent() {
  console.log('\n🔍 PROBLEM IDENTIFICATION:');
  console.log('\nBased on the screenshot showing columns:');
  console.log('DATE | SETUP | SESSION | DIRECTION | MARKET | ENTRY | EXIT | R-MULTIPLE | P&L');
  console.log('\nThis matches: RecentTradesTable (Trading Dashboard)');
  console.log('\n❌ POTENTIAL ISSUES:');
  console.log('1. Our fix is in DataTransformers.convertToTradeFormat');
  console.log('2. But RecentTradesPanel uses different data source');
  console.log('3. Multiple components might be rendering');
  console.log('4. Caching might prevent fix from taking effect');
}

function checkCurrentRoute() {
  console.log('\n🗺️ CURRENT ROUTE ANALYSIS:');
  console.log(`Current URL: ${window.location.href}`);
  console.log(`Current pathname: ${window.location.pathname}`);
  
  if (window.location.pathname === '/') {
    console.log('✅ On main dashboard - should use RecentTradesTable');
    console.log('✅ Our fix should apply here');
  } else if (window.location.pathname.includes('performance')) {
    console.log('⚠️ On performance dashboard - uses RecentTradesPanel');
    console.log('❌ Our fix does NOT apply here');
  } else if (window.location.pathname.includes('journal')) {
    console.log('⚠️ On trade journal - uses TradeList');
    console.log('✅ Has its own sorting in useTradeList');
  } else if (window.location.pathname.includes('analysis')) {
    console.log('⚠️ On trade analysis - uses TradesTable');
    console.log('✅ Has its own sorting in useTradesTableData');
  }
}

function checkForMultipleComponents() {
  console.log('\n🔍 CHECKING FOR MULTIPLE COMPONENTS:');
  
  // Check for RecentTradesTable
  const recentTradesTable = document.querySelector('[class*="TableContainer"]');
  if (recentTradesTable) {
    console.log('✅ Found RecentTradesTable container');
    const title = recentTradesTable.querySelector('[class*="TableTitle"]');
    if (title && title.textContent.includes('Recent Trades')) {
      console.log('✅ Confirmed RecentTradesTable with "Recent Trades" title');
    }
  }
  
  // Check for RecentTradesPanel
  const recentTradesPanel = document.querySelector('table');
  if (recentTradesPanel) {
    const headers = Array.from(recentTradesPanel.querySelectorAll('th')).map(th => th.textContent);
    console.log('📋 Found table headers:', headers);
    
    if (headers.includes('Symbol') && headers.includes('Result')) {
      console.log('⚠️ This looks like RecentTradesPanel (has Symbol, Result columns)');
      console.log('❌ Our fix does NOT apply to RecentTradesPanel');
    } else if (headers.includes('Setup') && headers.includes('Market')) {
      console.log('✅ This looks like RecentTradesTable (has Setup, Market columns)');
      console.log('✅ Our fix should apply here');
    }
  }
}

function checkDataFlow() {
  console.log('\n🔄 DATA FLOW VERIFICATION:');
  console.log('Looking for our debug logs...');
  
  // Check if our debug logs appeared
  const logs = [];
  const originalLog = console.log;
  console.log = function(...args) {
    if (args[0] && typeof args[0] === 'string') {
      if (args[0].includes('🔄 DataTransformers.convertToTradeFormat')) {
        logs.push('✅ Found DataTransformers.convertToTradeFormat call');
      }
      if (args[0].includes('📅 Raw trade dates')) {
        logs.push('✅ Found raw trade dates log');
      }
      if (args[0].includes('📅 Sorted trade dates')) {
        logs.push('✅ Found sorted trade dates log');
      }
      if (args[0].includes('🎯 TradingDashboardContainer')) {
        logs.push('✅ Found TradingDashboardContainer log');
      }
    }
    originalLog.apply(console, args);
  };
  
  setTimeout(() => {
    console.log = originalLog;
    console.log('\n📊 DEBUG LOG RESULTS:');
    if (logs.length > 0) {
      logs.forEach(log => console.log(log));
      console.log('✅ Our fix is being executed');
    } else {
      console.log('❌ No debug logs found - our fix is NOT being executed');
      console.log('🔧 POSSIBLE CAUSES:');
      console.log('1. Wrong component is rendering');
      console.log('2. Data is cached and not refreshing');
      console.log('3. Different data source is being used');
      console.log('4. Component is not re-rendering');
    }
  }, 3000);
}

function generateActionPlan() {
  console.log('\n📋 ACTION PLAN:');
  console.log('\n1. VERIFY COMPONENT:');
  console.log('   - Check if we\'re looking at RecentTradesTable or RecentTradesPanel');
  console.log('   - Confirm route and component mapping');
  
  console.log('\n2. CHECK DATA FLOW:');
  console.log('   - Look for our debug logs in console');
  console.log('   - Verify DataTransformers.convertToTradeFormat is called');
  
  console.log('\n3. FORCE REFRESH:');
  console.log('   - Clear browser cache');
  console.log('   - Force component re-render');
  console.log('   - Check if data is memoized/cached');
  
  console.log('\n4. ALTERNATIVE FIXES:');
  console.log('   - If RecentTradesPanel: fix useDashboardData hook');
  console.log('   - If caching issue: clear memoization');
  console.log('   - If wrong component: identify correct one');
}

// Main execution
function runComponentMappingAnalysis() {
  analyzeComponentMapping();
  identifyProblemComponent();
  checkCurrentRoute();
  checkForMultipleComponents();
  checkDataFlow();
  generateActionPlan();
}

// Export for manual use
window.componentMapping = {
  runComponentMappingAnalysis,
  analyzeComponentMapping,
  identifyProblemComponent,
  checkCurrentRoute,
  checkForMultipleComponents,
  checkDataFlow,
  TRADE_DISPLAY_COMPONENTS
};

// Auto-run
runComponentMappingAnalysis();
