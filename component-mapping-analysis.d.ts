declare function analyzeComponentMapping(): void;
declare function identifyProblemComponent(): void;
declare function checkCurrentRoute(): void;
declare function checkForMultipleComponents(): void;
declare function checkDataFlow(): void;
declare function generateActionPlan(): void;
declare function runComponentMappingAnalysis(): void;
declare namespace TRADE_DISPLAY_COMPONENTS {
    namespace RecentTradesTable {
        let location: string;
        let usedIn: string[];
        let dataSource: string;
        let columns: string[];
        let dataType: string;
        let sorting: string;
        let route: string;
    }
    namespace RecentTradesPanel {
        let location_1: string;
        export { location_1 as location };
        let usedIn_1: string[];
        export { usedIn_1 as usedIn };
        let dataSource_1: string;
        export { dataSource_1 as dataSource };
        let columns_1: string[];
        export { columns_1 as columns };
        let dataType_1: string;
        export { dataType_1 as dataType };
        let sorting_1: string;
        export { sorting_1 as sorting };
        let route_1: string;
        export { route_1 as route };
    }
    namespace TradeList {
        let location_2: string;
        export { location_2 as location };
        let usedIn_2: string[];
        export { usedIn_2 as usedIn };
        let dataSource_2: string;
        export { dataSource_2 as dataSource };
        let columns_2: string[];
        export { columns_2 as columns };
        let dataType_2: string;
        export { dataType_2 as dataType };
        let sorting_2: string;
        export { sorting_2 as sorting };
        let route_2: string;
        export { route_2 as route };
    }
    namespace TradesTable {
        let location_3: string;
        export { location_3 as location };
        let usedIn_3: string[];
        export { usedIn_3 as usedIn };
        let dataSource_3: string;
        export { dataSource_3 as dataSource };
        let columns_3: string[];
        export { columns_3 as columns };
        let dataType_3: string;
        export { dataType_3 as dataType };
        let sorting_3: string;
        export { sorting_3 as sorting };
        let route_3: string;
        export { route_3 as route };
    }
}
//# sourceMappingURL=component-mapping-analysis.d.ts.map