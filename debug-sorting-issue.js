/**
 * Focused Recent Trades Sorting Debug Script
 * 
 * This script will systematically identify why the sorting fix isn't working
 */

console.log('🔍 FOCUSED SORTING DEBUG ANALYSIS');
console.log('='.repeat(50));

// Step 1: Check if we're on the correct page
function verifyCurrentPage() {
  console.log('\n📍 Step 1: Verifying current page...');
  console.log(`URL: ${window.location.href}`);
  console.log(`Pathname: ${window.location.pathname}`);
  
  if (window.location.pathname !== '/') {
    console.log('❌ NOT on main dashboard! Navigate to http://localhost:3000/');
    return false;
  }
  
  console.log('✅ On main dashboard - correct page');
  return true;
}

// Step 2: Find the Recent Trades component
function findRecentTradesComponent() {
  console.log('\n🎯 Step 2: Finding Recent Trades component...');
  
  // Look for the specific table structure we expect
  const tables = document.querySelectorAll('table');
  console.log(`Found ${tables.length} tables on page`);
  
  let recentTradesTable = null;
  
  tables.forEach((table, index) => {
    const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent?.trim());
    console.log(`Table ${index + 1} headers:`, headers);
    
    // Check if this matches RecentTradesTable headers
    if (headers.includes('Setup') && headers.includes('Market') && headers.includes('R-Multiple')) {
      console.log('✅ Found RecentTradesTable!');
      recentTradesTable = table;
    } else if (headers.includes('Symbol') && headers.includes('Result')) {
      console.log('⚠️ Found RecentTradesPanel (wrong component)');
    }
  });
  
  if (!recentTradesTable) {
    console.log('❌ RecentTradesTable not found!');
    return null;
  }
  
  return recentTradesTable;
}

// Step 3: Analyze the data in the table
function analyzeTableData(table) {
  console.log('\n📊 Step 3: Analyzing table data...');
  
  const rows = table.querySelectorAll('tbody tr');
  console.log(`Found ${rows.length} data rows`);
  
  if (rows.length === 0) {
    console.log('❌ No data rows found!');
    return [];
  }
  
  const trades = [];
  rows.forEach((row, index) => {
    const cells = Array.from(row.cells).map(cell => cell.textContent?.trim());
    if (cells.length > 0) {
      trades.push({
        index: index + 1,
        date: cells[0] || 'No date',
        setup: cells[1] || 'No setup',
        session: cells[2] || 'No session',
        direction: cells[3] || 'No direction',
        market: cells[4] || 'No market',
        entry: cells[5] || 'No entry',
        exit: cells[6] || 'No exit',
        rMultiple: cells[7] || 'No R-Multiple',
        pnl: cells[8] || 'No P&L'
      });
    }
  });
  
  console.log('\n📋 Current table data:');
  trades.forEach(trade => {
    console.log(`${trade.index}. Date: ${trade.date}, Market: ${trade.market}, Direction: ${trade.direction}`);
  });
  
  return trades;
}

// Step 4: Check date order
function checkDateOrder(trades) {
  console.log('\n📅 Step 4: Checking date order...');
  
  if (trades.length < 2) {
    console.log('⚠️ Not enough trades to check order');
    return;
  }
  
  const dates = trades.map(t => t.date).filter(d => d && d !== 'No date');
  console.log('Dates found:', dates);
  
  let isNewestFirst = true;
  let isOldestFirst = true;
  
  for (let i = 1; i < dates.length; i++) {
    const prevDate = new Date(dates[i - 1]);
    const currDate = new Date(dates[i]);
    
    if (prevDate < currDate) {
      isNewestFirst = false;
    }
    if (prevDate > currDate) {
      isOldestFirst = false;
    }
  }
  
  console.log('\n📊 Date order analysis:');
  if (isNewestFirst) {
    console.log('✅ Dates are in NEWEST FIRST order (correct)');
  } else if (isOldestFirst) {
    console.log('❌ Dates are in OLDEST FIRST order (incorrect)');
  } else {
    console.log('⚠️ Dates are in MIXED order');
  }
  
  return { isNewestFirst, isOldestFirst };
}

// Step 5: Check for debug logs
function checkDebugLogs() {
  console.log('\n🔍 Step 5: Checking for our debug logs...');
  
  // Capture console logs for a few seconds
  const originalLog = console.log;
  const capturedLogs = [];
  
  console.log = function(...args) {
    if (args[0] && typeof args[0] === 'string') {
      capturedLogs.push(args[0]);
    }
    originalLog.apply(console, args);
  };
  
  // Force a data refresh
  console.log('🔄 Forcing data refresh...');
  if (window.location.pathname === '/') {
    // Try to trigger a refresh by reloading
    setTimeout(() => {
      window.location.reload();
    }, 1000);
  }
  
  // Check logs after a delay
  setTimeout(() => {
    console.log = originalLog;
    
    console.log('\n📋 Debug log analysis:');
    const relevantLogs = capturedLogs.filter(log => 
      log.includes('DataTransformers') || 
      log.includes('TradingDashboardContainer') ||
      log.includes('Raw trade dates') ||
      log.includes('Sorted trade dates')
    );
    
    if (relevantLogs.length > 0) {
      console.log('✅ Found our debug logs:');
      relevantLogs.forEach(log => console.log(`  - ${log}`));
    } else {
      console.log('❌ No debug logs found - our fix is not being executed!');
    }
  }, 3000);
}

// Step 6: Check IndexedDB directly
async function checkIndexedDBOrder() {
  console.log('\n💾 Step 6: Checking IndexedDB data order...');
  
  return new Promise((resolve) => {
    const request = indexedDB.open('adhd-trading-dashboard', 1);
    
    request.onsuccess = function(event) {
      const db = event.target.result;
      const transaction = db.transaction(['trades'], 'readonly');
      const store = transaction.objectStore('trades');
      const getAllRequest = store.getAll();
      
      getAllRequest.onsuccess = function() {
        const trades = getAllRequest.result;
        console.log(`Found ${trades.length} trades in IndexedDB`);
        
        if (trades.length > 0) {
          console.log('\n📊 IndexedDB trade order analysis:');
          
          // Show first 5 trades in different orders
          const byId = [...trades].sort((a, b) => (a.id || 0) - (b.id || 0));
          console.log('\n🔢 By ID (insertion order):');
          byId.slice(0, 5).forEach((trade, i) => {
            console.log(`  ${i + 1}. ID: ${trade.id}, Date: ${trade.date}`);
          });
          
          const byDateAsc = [...trades].sort((a, b) => new Date(a.date) - new Date(b.date));
          console.log('\n📅 By Date (oldest first):');
          byDateAsc.slice(0, 5).forEach((trade, i) => {
            console.log(`  ${i + 1}. ID: ${trade.id}, Date: ${trade.date}`);
          });
          
          const byDateDesc = [...trades].sort((a, b) => new Date(b.date) - new Date(a.date));
          console.log('\n📅 By Date (newest first - what we want):');
          byDateDesc.slice(0, 5).forEach((trade, i) => {
            console.log(`  ${i + 1}. ID: ${trade.id}, Date: ${trade.date}`);
          });
        }
        
        resolve(trades);
      };
    };
    
    request.onerror = function() {
      console.log('❌ Error accessing IndexedDB');
      resolve([]);
    };
  });
}

// Step 7: Generate action plan
function generateActionPlan(tableData, dateOrder) {
  console.log('\n📋 Step 7: Action Plan...');
  
  if (!tableData || tableData.length === 0) {
    console.log('❌ ISSUE: No table data found');
    console.log('🔧 ACTION: Check if component is rendering correctly');
    return;
  }
  
  if (dateOrder && dateOrder.isOldestFirst) {
    console.log('❌ ISSUE: Dates are in oldest-first order');
    console.log('🔧 ACTIONS:');
    console.log('1. Check if our debug logs appear (Step 5)');
    console.log('2. If no logs: Our fix is not being executed');
    console.log('3. If logs appear: Check the sorting logic');
    console.log('4. Compare IndexedDB order with displayed order');
  } else if (dateOrder && dateOrder.isNewestFirst) {
    console.log('✅ SUCCESS: Dates are in newest-first order');
    console.log('🎉 The sorting fix is working correctly!');
  } else {
    console.log('⚠️ ISSUE: Date order is unclear');
    console.log('🔧 ACTION: Check data quality and date formats');
  }
}

// Main execution function
async function runFocusedDebug() {
  console.log('🚀 Starting focused sorting debug...');
  
  // Step 1: Verify page
  if (!verifyCurrentPage()) {
    return;
  }
  
  // Step 2: Find component
  const table = findRecentTradesComponent();
  if (!table) {
    console.log('❌ Cannot proceed - Recent Trades table not found');
    return;
  }
  
  // Step 3: Analyze data
  const tableData = analyzeTableData(table);
  
  // Step 4: Check order
  const dateOrder = checkDateOrder(tableData);
  
  // Step 5: Check logs
  checkDebugLogs();
  
  // Step 6: Check IndexedDB
  await checkIndexedDBOrder();
  
  // Step 7: Action plan
  generateActionPlan(tableData, dateOrder);
  
  console.log('\n✅ Focused debug analysis complete!');
}

// Export for manual use
window.debugSorting = {
  runFocusedDebug,
  verifyCurrentPage,
  findRecentTradesComponent,
  analyzeTableData,
  checkDateOrder,
  checkDebugLogs,
  checkIndexedDBOrder
};

// Auto-run
runFocusedDebug();
