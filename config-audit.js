#!/usr/bin/env node

/**
 * Comprehensive Configuration Audit Tool
 * 
 * Audits all configuration files across the ADHD Trading Dashboard monorepo
 * for version consistency and proper configuration alignment.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration file patterns to search for
const CONFIG_PATTERNS = {
  packageManagement: ['package.json', 'yarn.lock', '.yarnrc', 'lerna.json'],
  buildTools: ['vite.config.js', 'vite.config.ts', 'webpack.config.js', 'rollup.config.js'],
  typescript: ['tsconfig.json', 'tsconfig.build.json', 'tsconfig.node.json'],
  development: ['.env', '.env.local', '.env.development', '.env.production', 'nodemon.json', 'jest.config.js', 'vitest.config.js'],
  linting: ['.eslintrc', '.eslintrc.js', '.eslintrc.json', '.prettierrc', '.prettierrc.js', '.prettierrc.json', '.editorconfig']
};

// Critical dependencies to track
const CRITICAL_DEPS = [
  'react', 'react-dom', 'typescript', 'vite', '@vitejs/plugin-react',
  'styled-components', '@types/react', '@types/react-dom'
];

class ConfigurationAuditor {
  constructor() {
    this.rootDir = process.cwd();
    this.packages = ['', 'packages/shared', 'packages/dashboard'];
    this.findings = {
      configFiles: {},
      versionConsistency: {},
      configAlignment: {},
      conflicts: [],
      recommendations: []
    };
  }

  async audit() {
    console.log('🔍 Starting comprehensive configuration audit...\n');
    
    // Step 1: Inventory all configuration files
    await this.inventoryConfigFiles();
    
    // Step 2: Check version consistency
    await this.checkVersionConsistency();
    
    // Step 3: Validate configuration alignment
    await this.validateConfigAlignment();
    
    // Step 4: Generate report
    this.generateReport();
  }

  async inventoryConfigFiles() {
    console.log('📋 Inventorying configuration files...');
    
    for (const pkg of this.packages) {
      const pkgPath = path.join(this.rootDir, pkg);
      const pkgName = pkg || 'root';
      
      this.findings.configFiles[pkgName] = {};
      
      for (const [category, patterns] of Object.entries(CONFIG_PATTERNS)) {
        this.findings.configFiles[pkgName][category] = [];
        
        for (const pattern of patterns) {
          const filePath = path.join(pkgPath, pattern);
          if (fs.existsSync(filePath)) {
            this.findings.configFiles[pkgName][category].push({
              file: pattern,
              path: filePath,
              size: fs.statSync(filePath).size
            });
          }
        }
      }
    }
  }

  async checkVersionConsistency() {
    console.log('🔄 Checking version consistency...');
    
    const packageJsons = {};
    
    // Read all package.json files
    for (const pkg of this.packages) {
      const pkgPath = path.join(this.rootDir, pkg);
      const packageJsonPath = path.join(pkgPath, 'package.json');
      
      if (fs.existsSync(packageJsonPath)) {
        const content = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        packageJsons[pkg || 'root'] = content;
      }
    }
    
    // Check critical dependencies
    for (const dep of CRITICAL_DEPS) {
      const versions = {};
      
      for (const [pkgName, content] of Object.entries(packageJsons)) {
        const allDeps = {
          ...content.dependencies,
          ...content.devDependencies,
          ...content.peerDependencies
        };
        
        if (allDeps[dep]) {
          versions[pkgName] = allDeps[dep];
        }
      }
      
      this.findings.versionConsistency[dep] = versions;
      
      // Check for inconsistencies
      const uniqueVersions = [...new Set(Object.values(versions))];
      if (uniqueVersions.length > 1) {
        this.findings.conflicts.push({
          type: 'version_mismatch',
          dependency: dep,
          versions: versions,
          severity: 'high'
        });
      }
    }
  }

  async validateConfigAlignment() {
    console.log('⚙️  Validating configuration alignment...');
    
    // Check TypeScript configurations
    await this.checkTypeScriptConfig();
    
    // Check Vite configurations
    await this.checkViteConfig();
    
    // Check build targets
    await this.checkBuildTargets();
  }

  async checkTypeScriptConfig() {
    const tsConfigs = {};
    
    for (const pkg of this.packages) {
      const pkgPath = path.join(this.rootDir, pkg);
      const tsConfigPath = path.join(pkgPath, 'tsconfig.json');
      
      if (fs.existsSync(tsConfigPath)) {
        const content = JSON.parse(fs.readFileSync(tsConfigPath, 'utf8'));
        tsConfigs[pkg || 'root'] = content;
      }
    }
    
    this.findings.configAlignment.typescript = tsConfigs;
    
    // Check for common issues
    for (const [pkgName, config] of Object.entries(tsConfigs)) {
      if (config.compilerOptions) {
        // Check JSX configuration
        if (config.compilerOptions.jsx && config.compilerOptions.jsx !== 'react-jsx') {
          this.findings.conflicts.push({
            type: 'config_mismatch',
            package: pkgName,
            issue: `JSX setting is ${config.compilerOptions.jsx}, should be 'react-jsx'`,
            severity: 'medium'
          });
        }
        
        // Check module resolution
        if (config.compilerOptions.moduleResolution !== 'node') {
          this.findings.conflicts.push({
            type: 'config_mismatch',
            package: pkgName,
            issue: `moduleResolution is ${config.compilerOptions.moduleResolution}, should be 'node'`,
            severity: 'low'
          });
        }
      }
    }
  }

  async checkViteConfig() {
    const viteConfigs = {};
    
    for (const pkg of this.packages) {
      const pkgPath = path.join(this.rootDir, pkg);
      const viteConfigPath = path.join(pkgPath, 'vite.config.ts');
      
      if (fs.existsSync(viteConfigPath)) {
        const content = fs.readFileSync(viteConfigPath, 'utf8');
        viteConfigs[pkg || 'root'] = {
          hasReactPlugin: content.includes('@vitejs/plugin-react'),
          hasStyledComponents: content.includes('babel-plugin-styled-components'),
          hasJsxRuntime: content.includes('jsxRuntime'),
          content: content.substring(0, 500) + '...' // Truncate for report
        };
      }
    }
    
    this.findings.configAlignment.vite = viteConfigs;
  }

  async checkBuildTargets() {
    // Check if build targets are compatible across packages
    const buildTargets = {};
    
    for (const pkg of this.packages) {
      const pkgPath = path.join(this.rootDir, pkg);
      const packageJsonPath = path.join(pkgPath, 'package.json');
      
      if (fs.existsSync(packageJsonPath)) {
        const content = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        buildTargets[pkg || 'root'] = {
          type: content.type,
          main: content.main,
          module: content.module,
          exports: content.exports
        };
      }
    }
    
    this.findings.configAlignment.buildTargets = buildTargets;
  }

  generateReport() {
    console.log('\n📊 CONFIGURATION AUDIT REPORT');
    console.log('='.repeat(50));
    
    // Configuration Files Summary
    console.log('\n📋 Configuration Files Inventory:');
    for (const [pkgName, categories] of Object.entries(this.findings.configFiles)) {
      console.log(`\n  ${pkgName}:`);
      for (const [category, files] of Object.entries(categories)) {
        if (files.length > 0) {
          console.log(`    ${category}: ${files.map(f => f.file).join(', ')}`);
        }
      }
    }
    
    // Version Consistency
    console.log('\n🔄 Version Consistency:');
    for (const [dep, versions] of Object.entries(this.findings.versionConsistency)) {
      const uniqueVersions = [...new Set(Object.values(versions))];
      const status = uniqueVersions.length === 1 ? '✅' : '❌';
      console.log(`  ${status} ${dep}: ${JSON.stringify(versions)}`);
    }
    
    // Conflicts and Issues
    console.log('\n⚠️  Conflicts and Issues:');
    if (this.findings.conflicts.length === 0) {
      console.log('  ✅ No conflicts found!');
    } else {
      for (const conflict of this.findings.conflicts) {
        console.log(`  ❌ ${conflict.type}: ${conflict.issue || conflict.dependency}`);
        console.log(`     Severity: ${conflict.severity}`);
        if (conflict.versions) {
          console.log(`     Versions: ${JSON.stringify(conflict.versions)}`);
        }
      }
    }
    
    // Recommendations
    this.generateRecommendations();
    console.log('\n💡 Recommendations:');
    if (this.findings.recommendations.length === 0) {
      console.log('  ✅ Configuration is optimal!');
    } else {
      for (const rec of this.findings.recommendations) {
        console.log(`  • ${rec}`);
      }
    }
    
    console.log('\n✅ Audit complete!');
  }

  generateRecommendations() {
    // Generate recommendations based on findings
    if (this.findings.conflicts.length > 0) {
      this.findings.recommendations.push('Run `yarn deps:sync` to resolve version inconsistencies');
    }
    
    // Check for missing critical configurations
    const dashboardVite = this.findings.configAlignment.vite['packages/dashboard'];
    if (dashboardVite && !dashboardVite.hasJsxRuntime) {
      this.findings.recommendations.push('Add jsxRuntime: "automatic" to Vite React plugin configuration');
    }
    
    // Check TypeScript configurations
    const tsConfigs = this.findings.configAlignment.typescript;
    for (const [pkgName, config] of Object.entries(tsConfigs)) {
      if (config.compilerOptions && !config.compilerOptions.esModuleInterop) {
        this.findings.recommendations.push(`Enable esModuleInterop in ${pkgName} tsconfig.json`);
      }
    }
  }
}

// Run the audit
const auditor = new ConfigurationAuditor();
auditor.audit().catch(console.error);
